<script setup lang="ts">
import { RouterView } from 'vue-router'
import theme from '@/assets/styles/ant-theme.json'
import zhCN from 'ant-design-vue/es/locale/zh_CN'
</script>

<template>
  <a-config-provider :locale="zhCN" :autoInsertSpaceInButton="false" :theme="theme">
    <RouterView />
  </a-config-provider>
</template>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  height: 100%;
  overflow: hidden; /* 防止body出现滚动条 */
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

#app {
  height: 100vh;
  width: 100vw;
  overflow: hidden; /* 防止app容器出现滚动条 */
}
</style>
