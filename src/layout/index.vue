<template>
  <div class="layout-content">
    <div class="layout-asider">
      <BaseSide />
    </div>
    <div class="layout-main-wrapper">
      <div class="layout-header">
        <BaseHeader />
      </div>
      <div class="layout-main">
        <router-view />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import BaseSide from './BaseSide.vue'
import BaseHeader from './BaseHeader.vue'
</script>

<style lang="scss" scoped>
.layout-content {
  display: flex;
  height: 100vh;
  overflow: hidden; // 防止整个页面出现滚动条

  .layout-asider {
    height: 100vh; // 确保左侧菜单栏覆盖整个页面高度
    flex-shrink: 0; // 防止侧边栏被压缩
  }

  .layout-main-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-width: 0; // 防止flex子元素溢出
    overflow: hidden; // 防止主区域溢出

    .layout-header {
      height: 64px;
      flex-shrink: 0; // 防止头部被压缩
    }

    .layout-main {
      flex: 1; // 占据剩余空间
      overflow: auto; // 当内容超出时显示滚动条
      height: calc(100% - 64px);
    }
  }
}
</style>
