<template>
  <div class="tabs-row">
    <div class="tabs">
      <div
        v-for="(tab, index) in tabs"
        :key="index"
        class="tabs-item"
        :class="{ selected: tab.active }"
        @click="handleTabClick(tab, index)"
      >
        <div class="tabs-item-title-wrapper">
          <component :is="tab.icon" />
          <span class="title">{{ tab.name }}</span>
        </div>
        <a-dropdown trigger="click" @click.stop>
          <a-button class="more-btn" type="text" size="small" :icon="h(MoreOutlined)" />
          <template #overlay>
            <a-menu @click="handleMenuClick($event, tab, index)">
              <a-menu-item key="edit"> 编辑 </a-menu-item>
              <a-menu-divider />
              <a-menu-item key="copy"> 复制 </a-menu-item>
              <a-menu-divider />
              <a-menu-item key="delete">删除</a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </div>
    </div>
    <div class="tabs-add-btn">
      <a-button
        class="add-btn"
        type="text"
        size="small"
        :icon="h(PlusOutlined)"
        @click="handleAddClick"
      />
    </div>
  </div>
  <Edit :open="showEdit" :columns="props.columns" @close="showEdit = false" />
</template>

<script setup lang="ts">
import { ref, h } from 'vue'
import { MoreOutlined, PlusOutlined } from '@ant-design/icons-vue'
import Edit from './Edit-UI.vue'

// 定义 Tab 接口
interface Tab {
  name: string
  icon: any
  active: boolean
  [key: string]: any // 允许额外的属性
}
// 定义 Props
interface Props {
  tabs: Tab[]
  columns?: any[]
}
// 定义 Emits
interface Emits {
  (e: 'tab-click', tab: Tab, index: number): void
  (e: 'more-click', tab: Tab, index: number): void
  (e: 'add-click'): void
}
const props = withDefaults(defineProps<Props>(), {
  columns: () => [],
})
const emit = defineEmits<Emits>()

const showEdit = ref<boolean>(false)

// 事件处理函数
const handleTabClick = (tab: Tab, index: number) => {
  emit('tab-click', tab, index)
}

// 添加
const handleAddClick = () => {
  emit('add-click')
}

// 菜单项点击处理
const handleMenuClick = (menuInfo: any, tab: Tab, index: number) => {
  console.log(menuInfo, tab, index)
  showEdit.value = true
}
</script>

<style lang="scss" scoped>
.tabs-row {
  display: flex;
  align-items: center;
  height: 47px;
  padding: 0 $spacing-lg;
  border-bottom: 1px solid $border-color;

  .tabs {
    display: flex;

    .tabs-item {
      position: relative;
      display: inline-flex;
      padding: $spacing-base $spacing-md;
      padding-right: $spacing-xs;
      cursor: pointer;

      &:first-child {
        padding-left: 0;
      }
      &.selected {
        .title {
          color: $primary-color;
        }

        .tabs-item-title-wrapper::after {
          background-color: $primary-color;
        }
      }

      .tabs-item-title-wrapper {
        cursor: pointer;
        align-items: center;
        display: flex;
        position: relative;

        &::after {
          content: '';
          background-color: transparent;
          height: 2px;
          position: absolute;
          bottom: -11px;
          left: 0;
          right: 0;
        }

        span.title {
          margin-left: $spacing-sm;
        }
      }

      .more-btn {
        margin-left: $spacing-xs;

        &::after {
          content: '';
          background-color: #e5e5e5;
          width: 1px;
          height: 24px;
          position: absolute;
          top: -2px;
          right: -5px;
        }
      }
    }
  }

  .tabs-add-btn {
    padding-left: $spacing-sm;
  }
}
</style>
