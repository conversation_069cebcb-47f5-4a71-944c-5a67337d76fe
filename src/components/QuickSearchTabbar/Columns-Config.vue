<template>
  <div class="columns-config">
    <!-- 操作按钮区域 -->
    <div class="actions-section">
      <a-space>
        <a-button @click="showAllColumns">全部显示</a-button>
        <a-button @click="hideAllColumns">全部隐藏</a-button>
      </a-space>
    </div>

    <!-- 搜索框 -->
    <div class="search-section">
      <a-input
        v-model:value="searchKeyword"
        placeholder="搜索字段"
        allow-clear
        size="small"
        class="search-input"
      >
        <template #prefix>
          <SearchOutlined style="font-size: 16px; color: #999" />
        </template>
      </a-input>
    </div>

    <!-- 字段列表 -->
    <div class="fields-section">
      <draggable
        v-model="sortedColumns"
        group="columns"
        handle=".drag-handle"
        :move="onDragMove"
        @end="onDragEnd"
        item-key="key"
        class="field-list"
      >
        <template #item="{ element }">
          <div
            v-show="isFieldVisible(element)"
            class="field-item"
            :class="{
              fixed: isFixed(element),
              hidden: !element.visible,
              disabled: isDisabled(element),
            }"
          >
            <div class="field-item-left">
              <!-- 拖拽图标 -->
              <span
                :class="{
                  'drag-handle': canDrag(element),
                  'drag-handle--disabled': !canDrag(element),
                }"
              >
                <HolderOutlined />
              </span>

              <!-- 字段名 -->
              <span class="field-name">{{ element.title }}</span>
            </div>

            <div class="actions">
              <!-- 固定/取消固定 -->
              <a-button
                type="text"
                size="small"
                :class="{ 'pin-fixed': isFixed(element) }"
                @click="toggleFixed(element)"
                :disabled="!element.visible"
              >
                <PushpinOutlined />
              </a-button>

              <!-- 显示/隐藏 -->
              <a-button type="text" size="small" @click="toggleVisible(element)">
                <component :is="element.visible ? EyeOutlined : EyeInvisibleOutlined" />
              </a-button>
            </div>
          </div>
        </template>
      </draggable>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import draggable from 'vuedraggable'
import {
  HolderOutlined,
  PushpinOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  SearchOutlined,
} from '@ant-design/icons-vue'

import type { TableColumn } from '@/common/types'

const props = defineProps<{
  columns: TableColumn[]
}>()
const emit = defineEmits<{
  (e: 'update', value: TableColumn[]): void
}>()

const searchKeyword = ref('')
// clone prop columns
const sortedColumns = ref<TableColumn[]>([...props.columns])

// 根据搜索关键词过滤字段
const isFieldVisible = (col: TableColumn) => {
  if (!searchKeyword.value) return true
  return col.title.toLowerCase().includes(searchKeyword.value.toLowerCase())
}

// 判断字段是否固定
const isFixed = (col: TableColumn) => col.fixed === true || col.fixed === 'left'
// 判断字段是否为禁用（如操作列）
const isDisabled = (col: TableColumn) => col.key === 'action'
// 允许拖拽的字段：非固定
const canDrag = (col: TableColumn) => !isFixed(col)

// 拖拽规则限制（不能拖动固定列、不能拖到固定列前）
function onDragMove(e: any) {
  const { draggedContext, relatedContext } = e
  const dragged = draggedContext.element
  const related = relatedContext.element

  if (isFixed(dragged) || isFixed(related)) return false
  if (isDisabled(dragged) || isDisabled(related)) return false
  return true
}

function onDragEnd() {
  emit('update', sortedColumns.value)
}

// 全部显示
function showAllColumns() {
  sortedColumns.value.forEach((col) => {
    col.visible = true
  })
  emit('update', sortedColumns.value)
}

// 全部隐藏
function hideAllColumns() {
  sortedColumns.value.forEach((col) => {
    col.visible = false
  })
  emit('update', sortedColumns.value)
}

// 切换显示/隐藏
function toggleVisible(col: TableColumn) {
  col.visible = !col.visible
  emit('update', sortedColumns.value)
}

// 切换固定/取消固定
function toggleFixed(col: TableColumn) {
  if (!col.visible) return
  col.fixed = isFixed(col) ? false : 'left'
  reorderByFixed()
  emit('update', sortedColumns.value)
}

// 根据固定状态自动重排
function reorderByFixed() {
  const fixed = sortedColumns.value.filter((c) => isFixed(c) && !isDisabled(c))
  const nonFixed = sortedColumns.value.filter((c) => !isFixed(c) && !isDisabled(c))
  const disabled = sortedColumns.value.filter((c) => isDisabled(c))
  sortedColumns.value = [...fixed, ...nonFixed, ...disabled]
}

// 监听 props.columns 变化，同步到内部状态
watch(
  () => props.columns,
  (newColumns) => {
    sortedColumns.value = [...newColumns]
  },
  { deep: true },
)
</script>

<style lang="scss" scoped>
.columns-config {
  display: flex;
  flex-direction: column;
  height: 95%;
}

.actions-section {
  flex-shrink: 0;
  margin-bottom: 16px;
}

.search-section {
  flex-shrink: 0;
  margin-bottom: 16px;

  .search-input {
    width: 100%;
    height: 32px;
    border-radius: 6px;
  }
}

.fields-section {
  flex: 1;
  min-height: 0;

  .field-list {
    height: 100%;
    overflow-y: auto;
  }

  .field-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 4px;
    border-radius: 4px;
    transition: background-color 0.2s;
    margin-bottom: 4px;

    &:hover {
      background-color: #f5f5f5;
    }

    &.fixed .drag-handle--disabled,
    &.hidden .field-name {
      color: #bbb;
    }

    &.disabled {
      pointer-events: none;
      opacity: 0.4;
    }
  }

  .field-item-left {
    display: flex;
    align-items: center;
  }

  .drag-handle,
  .drag-handle--disabled {
    cursor: move;
    margin-right: 8px;
    font-size: 16px;
    font-weight: bolder;
    color: #000;
  }

  .drag-handle--disabled {
    cursor: not-allowed;
  }

  .field-name {
    font-size: 14px;
  }

  .actions {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .pin-fixed {
    color: $primary-color;
  }
  .pin-fixed:hover {
    color: $primary-color;
  }
}

// 滚动条样式
.field-list::-webkit-scrollbar {
  width: 6px;
}

.field-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.field-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.field-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
