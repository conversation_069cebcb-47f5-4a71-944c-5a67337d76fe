<template>
  <a-drawer
    :open="props.open"
    :closable="false"
    :headerStyle="{ padding: '15px 24px' }"
    :body-style="{ padding: 0 }"
    width="768px"
    @close="handleClose"
  >
    <template #title>
      <div class="drawer-header">
        <div class="drawer-header-title">{{ drawerTitle }}</div>
        <div class="drawer-header-btn">
          <a-button
            class="drawer-header-btn-item"
            type="text"
            :icon="h(CloseOutlined)"
            @click="handleClose"
          />
        </div>
      </div>
    </template>

    <div class="drawer-content">
      <a-tabs
        v-model:activeKey="activeTab"
        tab-position="left"
        :tab-bar-style="{ width: '126px', background: '#f8f8f8' }"
      >
        <a-tab-pane key="basic" tab="基本信息">
          <div class="form-section">
            <h3 class="section-title">基本信息</h3>
            <a-form
              :model="formData"
              layout="horizontal"
              labelAlign="left"
              :label-col="{ span: 4 }"
              :wrapper-col="{ span: 20 }"
            >
              <a-form-item label="视图名称" required>
                <a-input v-model:value="formData.viewName" placeholder="全部" style="width: 100%" />
              </a-form-item>

              <a-form-item label="图标">
                <div class="icon-selector">
                  <div
                    v-for="icon in iconOptions"
                    :key="icon.value"
                    class="icon-item"
                    :class="{ active: formData.icon === icon.value }"
                    @click="formData.icon = icon.value"
                  >
                    <span :style="{ backgroundColor: icon.color }" class="icon-circle">
                      {{ icon.label }}
                    </span>
                  </div>
                </div>
              </a-form-item>

              <a-form-item label="二级分组">
                <a-select
                  v-model:value="formData.secondaryGroup"
                  placeholder="工单状态"
                  style="width: 100%"
                  :suffix-icon="h(DownOutlined)"
                >
                  <a-select-option value="工单状态">工单状态</a-select-option>
                  <a-select-option value="任务状态">任务状态</a-select-option>
                  <a-select-option value="项目状态">项目状态</a-select-option>
                </a-select>
              </a-form-item>

              <a-form-item label="行高">
                <div class="row-height-selector">
                  <a-radio-group v-model:value="formData.rowHeight" button-style="solid">
                    <a-radio-button value="low">低</a-radio-button>
                    <a-radio-button value="medium">中</a-radio-button>
                    <a-radio-button value="high">高</a-radio-button>
                  </a-radio-group>
                </div>
              </a-form-item>

              <a-form-item label="使用范围">
                <div class="scope-container">
                  <a-select
                    v-model:value="formData.scope"
                    mode="multiple"
                    style="width: 100%"
                    placeholder="Please select"
                    :options="[
                      { value: '所有人', label: '所有人' },
                      { value: '管理员', label: '管理员' },
                      { value: '普通用户', label: '普通用户' },
                    ]"
                  ></a-select>
                </div>
              </a-form-item>
            </a-form>
          </div>
        </a-tab-pane>
        <a-tab-pane key="level" tab="字段权限">
          <div class="form-section">
            <h3 class="section-title">字段权限</h3>
            <h5 class="section-subtitle">设置此视图下用户在列表最多查看的字段</h5>
            <div class="field-list">
              <div class="btn-actions">
                <a-space>
                  <a-button @click="handleAllOpen">全部打开</a-button>
                  <a-button @click="handleAllClose">全部关闭</a-button>
                </a-space>
              </div>
              <div class="list-item" v-for="item in authColumns" :key="item.key">
                <a-switch size="small" :checked="item.visible" @change="handleSwitchChange(item)" />
                <span>{{ item.title }}</span>
              </div>
            </div>
          </div>
        </a-tab-pane>
        <a-tab-pane key="config" tab="字段配置">
          <div class="form-section">
            <h3 class="section-title">字段配置</h3>
            <h5 class="section-subtitle">设置此视图下字段的顺序、显隐和固定</h5>
            <ColumnsConfig :columns="authColumns" @update="handleColumnsUpdate" />
          </div>
        </a-tab-pane>
        <a-tab-pane key="filter" tab="数据过滤">
          <div class="form-section">
            <h3 class="section-title">数据过滤</h3>
            <DataFilterPanel v-model="filterData" />
          </div>
        </a-tab-pane>
        <a-tab-pane key="sort" tab="默认排序">
          <div class="form-section">
            <h3 class="section-title">默认排序</h3>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>

    <template #footer>
      <div class="drawer-footer">
        <a-button class="drawer-footer__btn" type="default" @click="handleClose">取消</a-button>
        <a-button class="drawer-footer__btn" type="primary" @click="handleConfirm">确定</a-button>
      </div>
    </template>
  </a-drawer>
</template>

<script lang="ts" setup>
import { ref, h } from 'vue'
import { CloseOutlined, DownOutlined } from '@ant-design/icons-vue'
import { cloneDeep } from 'lodash'
import ColumnsConfig from './Columns-Config.vue'
import DataFilterPanel from './DataFilterPanel.vue'

const props = defineProps<{
  open: boolean
  columns: any[]
}>()
const emit = defineEmits(['close', 'confirm'])

const authColumns = ref(cloneDeep(props.columns))
const drawerTitle = ref<'编辑视图' | '复制视图'>('编辑视图')
const activeTab = ref('basic')
// 表单数据
const formData = ref({
  viewName: '全部',
  icon: 'default',
  secondaryGroup: '工单状态',
  rowHeight: 'low',
  scope: ['所有人'],
})
// 图标选项
const iconOptions = [
  { value: 'default', label: '≡', color: '#52c41a' },
  { value: 'list', label: '☰', color: '#52c41a' },
  { value: 'grid', label: '⊞', color: '#1890ff' },
  { value: 'calendar', label: '📅', color: '#faad14' },
  { value: 'chart', label: '📊', color: '#f5222d' },
]
const filterData = ref<any[]>([])

const handleAllOpen = () => {
  authColumns.value.forEach((item) => {
    item.visible = true
  })
}

const handleAllClose = () => {
  authColumns.value.forEach((item) => {
    item.visible = false
  })
}

const handleSwitchChange = (item: any) => {
  item.visible = !item.visible
}

const handleColumnsUpdate = (newColumns: any[]) => {
  authColumns.value = newColumns
}

const handleClose = () => {
  emit('close')
}

const handleConfirm = () => {
  console.log(filterData.value)
  emit('confirm')
}
</script>

<style lang="scss" scoped>
.drawer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .drawer-header-title {
    font-size: 16px;
    font-weight: 600;
    color: #111;
  }

  .drawer-header-btn {
    display: flex;
    align-items: center;
  }
}

.drawer-content {
  width: 100%;
  height: 100%;

  :deep(.ant-tabs) {
    height: 100%;

    .ant-tabs-nav {
      .ant-tabs-tab {
        padding: 8px 24px;
        margin: 0;
        border-radius: 0;
        border-right: none;
      }
    }

    .ant-tabs-tab-active {
      background-color: #fff;
    }

    .ant-tabs-content-holder {
      .ant-tabs-content {
        height: 100%;

        .ant-tabs-tabpane {
          padding: 24px;
          height: 100%;
          overflow-y: auto;
        }
      }
    }
  }

  .form-section {
    height: 100%;

    .section-title {
      font-size: 14px;
      font-weight: 600;
      color: #333;
      margin-bottom: 8px;
    }

    .section-subtitle {
      color: #999;
      margin-top: 8px;
      margin-bottom: 8px;
      font-size: 12px;
      font-weight: 400;
    }

    .icon-selector {
      display: flex;
      gap: 12px;

      .icon-item {
        cursor: pointer;

        .icon-circle {
          display: inline-flex;
          align-items: center;
          justify-content: center;
          width: 32px;
          height: 32px;
          border-radius: 6px;
          color: white;
          font-size: 14px;
          font-weight: 500;
        }

        &.active .icon-circle {
          box-shadow: 0 0 0 2px #1890ff;
        }
      }
    }

    .field-list {
      display: flex;
      flex-direction: column;
      gap: 12px;

      .btn-actions {
        margin-bottom: 16px;
      }

      .list-item {
        display: flex;
        align-items: center;
        gap: 6px;
      }
    }
  }
}

.drawer-footer {
  display: flex;
  gap: 12px;
  justify-content: flex-end;

  .drawer-footer__btn {
    min-width: 80px;
  }
}
</style>
