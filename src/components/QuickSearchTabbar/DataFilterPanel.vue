<template>
  <div class="data-filter-panel">
    <div class="panel-content">
      <div class="filters-list">
        <div v-for="(filter, index) in filters" :key="filter.id" class="filter-wrapper">
          <DataFilter
            :ref="(el) => setFilterRef(el, index)"
            @remove="() => removeFilter(index)"
            @change="(data) => updateFilter(index, data)"
          />
        </div>
        <div class="add-filter-btn">
          <a-button type="dashed" @click="addFilter" style="width: 100%"> + 添加筛选条件 </a-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick } from 'vue'
import DataFilter from './DataFilter.vue'

// 定义过滤条件接口
interface FilterItem {
  id: string
  connector?: 'AND' | 'OR'
  field?: string
  condition?: string
  value?: any
}

// 定义 Props
interface Props {
  modelValue?: FilterItem[]
}

// 定义 Emits
interface Emits {
  (e: 'update:modelValue', filters: FilterItem[]): void
  (e: 'apply', filters: FilterItem[]): void
  (e: 'clear'): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => [],
})

const emit = defineEmits<Emits>()

// 响应式数据
const filters = ref<FilterItem[]>([])
const filterRefs = ref<any[]>([])

// 设置过滤组件引用
const setFilterRef = (el: any, index: number) => {
  if (el) {
    filterRefs.value[index] = el
  }
}

// 生成唯一ID
const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

// 添加过滤条件
const addFilter = () => {
  const newFilter: FilterItem = {
    id: generateId(),
    connector: filters.value.length > 0 ? 'AND' : undefined,
  }
  filters.value.push(newFilter)

  nextTick(() => {
    emit('update:modelValue', filters.value)
  })
}

// 移除过滤条件
const removeFilter = (index: number) => {
  filters.value.splice(index, 1)

  // 如果删除的是第一个条件，需要移除下一个条件的连接符
  if (index === 0 && filters.value.length > 0) {
    filters.value[0].connector = undefined
  }

  // 如果没有过滤条件了，添加一个空的
  if (filters.value.length === 0) {
    addFilter()
  }

  emit('update:modelValue', filters.value)
}

// 更新过滤条件
const updateFilter = (index: number, data: any) => {
  if (filters.value[index]) {
    filters.value[index] = {
      ...filters.value[index],
      field: data.field,
      condition: data.condition,
      value: data.value,
    }
    emit('update:modelValue', filters.value)
  }
}

// 清空所有过滤条件
const clearAllFilters = () => {
  filters.value = []
  addFilter()
  emit('update:modelValue', filters.value)
  emit('clear')
}

// 应用过滤条件
const applyFilters = () => {
  const validFilters = filters.value.filter((filter) => filter.field && filter.condition)
  emit('apply', validFilters)
}

// 获取过滤条件数据
const getFiltersData = () => {
  return filters.value.filter((filter) => filter.field && filter.condition)
}

// 初始化过滤条件
if (props.modelValue.length > 0) {
  filters.value = [...props.modelValue]
} else {
  addFilter()
}

// 暴露方法给父组件
defineExpose({
  getFiltersData,
  clearAllFilters,
  applyFilters,
})
</script>

<style lang="scss" scoped>
.filter-wrapper {
  position: relative;
  margin-bottom: 12px;

  .filter-connector {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    padding-left: 20px;

    &::before {
      content: '';
      width: 1px;
      height: 20px;
      background-color: #d9d9d9;
      position: absolute;
      left: 40px;
      top: -20px;
    }
  }
}

.add-filter-btn {
  margin-top: 16px;
}
</style>
