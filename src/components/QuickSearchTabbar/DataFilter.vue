<template>
  <div class="data-filter">
    <div class="filter-row">
      <!-- 左侧：字段选择下拉框 -->
      <div class="filter-item">
        <a-select
          v-model:value="selectedField"
          placeholder="请选择字段"
          style="width: 160px"
          @change="handleFieldChange"
        >
          <a-select-option v-for="field in fieldOptions" :key="field.value" :value="field.value">
            {{ field.label }}
          </a-select-option>
        </a-select>
      </div>

      <!-- 中部：查询类型选择下拉框 -->
      <div class="filter-item">
        <a-select
          v-model:value="selectedCondition"
          placeholder="请选择条件"
          style="width: 140px"
          @change="handleConditionChange"
        >
          <a-select-option
            v-for="condition in currentConditionOptions"
            :key="condition.value"
            :value="condition.value"
          >
            {{ condition.label }}
          </a-select-option>
        </a-select>
      </div>

      <!-- 右侧：根据字段类型动态显示的输入控件 -->
      <div class="filter-item">
        <!-- input类型字段的输入框 -->
        <a-input
          v-if="showInputControl"
          v-model:value="inputValue"
          placeholder="请输入值"
          style="width: 200px"
        />

        <!-- number类型字段的范围输入框 -->
        <div v-else-if="showNumberRangeControl" class="number-range-control">
          <a-input-number
            v-model:value="numberRangeValue.min"
            placeholder="最小值"
            style="width: 95px"
            :min="0"
          />
          <span class="range-separator">-</span>
          <a-input-number
            v-model:value="numberRangeValue.max"
            placeholder="最大值"
            style="width: 95px"
            :min="0"
          />
        </div>

        <!-- select类型字段的下拉选择框 -->
        <a-select
          v-else-if="showSelectControl"
          v-model:value="selectValue"
          placeholder="请选择值"
          style="width: 200px"
          mode="multiple"
        >
          <a-select-option
            v-for="option in currentSelectOptions"
            :key="option.value"
            :value="option.value"
          >
            {{ option.label }}
          </a-select-option>
        </a-select>

        <!-- 当选择"为空"或"不为空"时不显示任何输入控件 -->
        <span v-else-if="showEmptyPlaceholder" class="empty-placeholder"> 无需输入值 </span>
      </div>

      <!-- 删除按钮 -->
      <div class="filter-item">
        <a-button type="text" :icon="h(CloseOutlined)" @click="handleRemove" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, h, watch } from 'vue'
import { CloseOutlined } from '@ant-design/icons-vue'

// 定义字段类型
type FieldType = 'input' | 'number' | 'select'

// 定义字段接口
interface FieldOption {
  value: string
  label: string
  type: FieldType
  selectOptions?: { value: string; label: string }[]
}

// 定义条件选项接口
interface ConditionOption {
  value: string
  label: string
}

// 定义 Emits
interface Emits {
  (e: 'remove'): void
  (e: 'change', filterData: any): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const selectedField = ref<string>('')
const selectedCondition = ref<string>('')
const inputValue = ref<string>('')
const numberRangeValue = ref({ min: undefined, max: undefined })
const selectValue = ref<string[]>([])

// 模拟字段数据
const fieldOptions = ref<FieldOption[]>([
  {
    value: 'workOrderNo',
    label: '工单编号',
    type: 'input',
  },
  {
    value: 'productCode',
    label: '产品编号',
    type: 'input',
  },
  {
    value: 'productName',
    label: '产品名称',
    type: 'input',
  },
  {
    value: 'quantity',
    label: '数量',
    type: 'number',
  },
  {
    value: 'price',
    label: '单价',
    type: 'number',
  },
  {
    value: 'status',
    label: '状态',
    type: 'select',
    selectOptions: [
      { value: 'pending', label: '待处理' },
      { value: 'processing', label: '处理中' },
      { value: 'completed', label: '已完成' },
      { value: 'cancelled', label: '已取消' },
    ],
  },
  {
    value: 'priority',
    label: '优先级',
    type: 'select',
    selectOptions: [
      { value: 'low', label: '低' },
      { value: 'medium', label: '中' },
      { value: 'high', label: '高' },
      { value: 'urgent', label: '紧急' },
    ],
  },
])

// 不同字段类型对应的条件选项
const conditionOptions = {
  input: [
    { value: 'equals', label: '等于' },
    { value: 'not_equals', label: '不等于' },
    { value: 'contains', label: '包含' },
    { value: 'not_contains', label: '不包含' },
    { value: 'is_empty', label: '为空' },
    { value: 'is_not_empty', label: '不为空' },
  ],
  number: [
    { value: 'in_range', label: '在范围内' },
    { value: 'not_in_range', label: '不在范围内' },
    { value: 'is_empty', label: '为空' },
    { value: 'is_not_empty', label: '不为空' },
  ],
  select: [
    { value: 'is_one_of', label: '是其中一个' },
    { value: 'is_not_any_of', label: '不是任何一个' },
    { value: 'is_empty', label: '为空' },
    { value: 'is_not_empty', label: '不为空' },
  ],
}

// 计算当前字段类型
const currentFieldType = computed<FieldType | null>(() => {
  const field = fieldOptions.value.find((f) => f.value === selectedField.value)
  return field?.type || null
})

// 计算当前条件选项
const currentConditionOptions = computed<ConditionOption[]>(() => {
  if (!currentFieldType.value) return []
  return conditionOptions[currentFieldType.value] || []
})

// 计算当前选择字段的下拉选项
const currentSelectOptions = computed(() => {
  const field = fieldOptions.value.find((f) => f.value === selectedField.value)
  return field?.selectOptions || []
})

// 计算是否显示输入框控件
const showInputControl = computed(() => {
  return (
    currentFieldType.value === 'input' &&
    selectedCondition.value &&
    !['is_empty', 'is_not_empty'].includes(selectedCondition.value)
  )
})

// 计算是否显示数字范围控件
const showNumberRangeControl = computed(() => {
  return (
    currentFieldType.value === 'number' &&
    selectedCondition.value &&
    ['in_range', 'not_in_range'].includes(selectedCondition.value)
  )
})

// 计算是否显示下拉选择控件
const showSelectControl = computed(() => {
  return (
    currentFieldType.value === 'select' &&
    selectedCondition.value &&
    ['is_one_of', 'is_not_any_of'].includes(selectedCondition.value)
  )
})

// 计算是否显示空值占位符
const showEmptyPlaceholder = computed(() => {
  return selectedCondition.value && ['is_empty', 'is_not_empty'].includes(selectedCondition.value)
})

// 字段选择变化处理
const handleFieldChange = () => {
  // 重置条件和值
  selectedCondition.value = ''
  resetValues()
  emitChange()
}

// 条件选择变化处理
const handleConditionChange = () => {
  // 重置值
  resetValues()
  emitChange()
}

// 重置所有输入值
const resetValues = () => {
  inputValue.value = ''
  numberRangeValue.value = { min: undefined, max: undefined }
  selectValue.value = []
}

// 删除当前过滤条件
const handleRemove = () => {
  emit('remove')
}

// 发送变化事件
const emitChange = () => {
  const filterData = {
    field: selectedField.value,
    condition: selectedCondition.value,
    value: getCurrentValue(),
  }
  emit('change', filterData)
}

// 获取当前值
const getCurrentValue = () => {
  if (showInputControl.value) {
    return inputValue.value
  } else if (showNumberRangeControl.value) {
    return numberRangeValue.value
  } else if (showSelectControl.value) {
    return selectValue.value
  } else if (showEmptyPlaceholder.value) {
    return null
  }
  return null
}

// 监听值变化
watch(
  [inputValue, numberRangeValue, selectValue],
  () => {
    if (selectedField.value && selectedCondition.value) {
      emitChange()
    }
  },
  { deep: true },
)
</script>

<style lang="scss" scoped>
.data-filter {
  .filter-row {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 0;

    .filter-item {
      display: flex;
      align-items: center;

      .number-range-control {
        display: flex;
        align-items: center;
        gap: 8px;

        .range-separator {
          color: #999;
          font-size: 14px;
        }
      }

      .empty-placeholder {
        color: #999;
        font-size: 14px;
        font-style: italic;
        padding: 0 12px;
        min-width: 200px;
        text-align: center;
      }
    }
  }
}

// 自定义下拉框样式
:deep(.ant-select) {
  .ant-select-selector {
    border-radius: 6px;
  }
}

// 自定义输入框样式
:deep(.ant-input) {
  border-radius: 6px;
}

// 自定义数字输入框样式
:deep(.ant-input-number) {
  border-radius: 6px;

  .ant-input-number-input {
    border-radius: 6px;
  }
}
</style>
