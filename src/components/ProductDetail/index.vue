<template>
  <div class="product-detail detail-box">
    <div class="info-block product-detail__left">
      <div class="product-row">
        <span class="product-row__label">产品编号</span>
        <span class="product-row__value">{{ productInfo?.productCode || '-' }}</span>
      </div>
      <div class="product-row">
        <span class="product-row__label">产品名称</span>
        <span class="product-row__value">{{ productInfo?.productName || '-' }}</span>
      </div>
      <div class="product-row">
        <span class="product-row__label">产品规格</span>
        <span class="product-row__value">{{ productInfo?.specification || '-' }}</span>
      </div>
    </div>
    <div class="info-block product-detail__right">
      <div class="stock-amount">{{ formatNumber(productInfo?.stockQuantity) }}</div>
      <div class="stock-unit-text">库存数量(<span>{{ productInfo?.unit || '-' }}</span>)</div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { formatNumber } from '@/utils'

interface ProductInfo {
  productCode?: string
  productName?: string
  specification?: string
  stockQuantity?: number
  unit?: string
}

defineProps<{
  productInfo?: ProductInfo
}>()
</script>

<style lang="scss" scoped>
.product-detail {
  .product-detail__left {
    flex: 2 0;
    display: flex;
    flex-direction: column;
    margin-right: 4px;

    .product-row {
      margin-bottom: 8px;
      font-size: 12px;
      line-height: 14px;

      &:last-child {
        margin-bottom: 0;
      }

      &__label {
        color: #999;
        margin-right: 8px;
      }

      &__value {
        color: #333;
        word-break: break-all;
      }
    }
  }

  .product-detail__right {
    color: #666;
    flex-direction: column;
    flex: 1 0;
    justify-content: center;
    align-items: center;
    width: 100%;
    display: flex;

    .stock-amount {
      text-align: center;
      color: #666;
      width: 100%;
      margin-bottom: 6px;
      font-family: DINAlternate-Bold, DINAlternate;
      font-size: 18px;
      font-weight: 700;
      line-height: 18px;
    }

    .stock-unit-text {
      white-space: nowrap;
      text-overflow: ellipsis;
      justify-content: center;
      width: 100%;
      padding: 0 3px;
      font-size: 12px;
      line-height: 14px;
      display: flex;
      overflow: hidden;
    }
  }
}

.detail-box {
  display: flex;
  margin-top: 4px;

  .info-block {
    background-color: #f8f9fa;
    border-radius: 6px;
    height: 76px;
    padding: 8px;
    overflow-y: auto;
    margin-right: 4px;

    &:last-child {
      margin-right: 0;
    }
  }
}
</style>
