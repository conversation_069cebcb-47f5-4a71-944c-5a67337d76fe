import { ref } from 'vue'

type RecordType = Object & { serialNumber: number; isModified: string }

/**
 * @method 表格行拖拽
 * @param dataSource 数据源
 */
export default function useDragTable<T extends RecordType = RecordType>(
  dataSource: T[],
  tableId: string,
) {
  /**拖拽起始行 */
  const sourceRecord = ref<Partial<T>>({})
  /**拖拽目标行 */
  const targetRecord = ref<Partial<T>>({})
  /**拖拽起始索引 */
  let oldIndex: number | null = null
  /**拖拽目标索引 */
  let newIndex: number | null = null

  /**
   * @method 自定义拖拽行
   * @param record 当前行数据
   * @param index 当前行索引
   */
  function customRow(record: T, index: number) {
    return {
      style: {
        cursor: 'pointer',
      },
      // 鼠标移入
      onMouseenter: (event: MouseEvent) => {
        // 兼容IE
        const ev = event || window.event
        const target = ev.target as HTMLElement
        target.draggable = true
      },
      // 开始拖拽
      onDragstart: (event: Event) => {
        // 兼容IE
        const ev = event || window.event
        ev.stopPropagation()
        // 得到源目标数据
        sourceRecord.value = record
        oldIndex = index
      },
      // 拖动元素经过的元素
      onDragover: (event: DragEvent) => {
        // 兼容 IE
        const ev = event || window.event
        // 阻止默认行为
        ev.preventDefault()
        ev.dataTransfer!.dropEffect = 'move' // 可以去掉拖动时那个＋号
        ev.dataTransfer!.effectAllowed = 'move' // 兼容火狐
        newIndex = index
      },
      // 拖动元素进入的元素
      ondragenter: (event: DragEvent) => {
        // 兼容 IE
        const ev = event || window.event
        // 阻止默认行为
        ev.preventDefault()
        const tableWrapper = document.getElementById(tableId)
        if (!tableWrapper) return

        const list = tableWrapper.getElementsByClassName('ant-table-row')
        const node = tableWrapper.getElementsByClassName('target')

        if (node.length) node[0].classList.remove('target')
        list[index].classList.add('target')
      },
      // 鼠标松开
      onDrop: (event: Event) => {
        const ev = event || window.event // 兼容IE
        ev.stopPropagation() // 阻止冒泡

        targetRecord.value = record // 得到目标数据
        newIndex = index // 将源数据插入目标数据前面

        // 移除目标元素的样式
        const tableWrapper = document.getElementById(tableId)
        if (tableWrapper) {
          const node = tableWrapper.getElementsByClassName('target')
          if (node.length) node[0].classList.remove('target')
        }

        if (newIndex === oldIndex) return
        // 如果从1拖到10,那么1-10之间的isModified都要改为1,或者从10拖到1
        // const startIndex = newIndex > oldIndex! ? oldIndex : newIndex
        // const endIndex = newIndex > oldIndex! ? newIndex : oldIndex
        // for (let i = startIndex; i! <= endIndex!; i!++) {
        //   dataSource[i!]['isModified'] = '1'
        // }

        // dataSource[oldIndex!].serialNumber = newIndex + 1
        // dataSource[newIndex!].serialNumber = oldIndex! + 1
        dataSource.splice(oldIndex!, 1)
        dataSource.splice(newIndex, 0, sourceRecord.value)
      },
    }
  }

  return {
    sourceRecord,
    targetRecord,
    oldIndex,
    newIndex,
    customRow,
  }
}
