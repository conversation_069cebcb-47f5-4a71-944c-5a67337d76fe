import { createRouter, createWebHistory } from 'vue-router'
import { routes } from './route'

import Layout from '@/layout/index.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/produce/report-workbench',
      name: 'ProduceReportWorkbench',
      component: () => import('../views/produce/pages/report-work-bench/ReportWorkbench.vue'),
      meta: {
        title: '报工工作台',
        layout: false, // 标记为不需要布局
      },
    },
    {
      path: '/',
      name: 'Layout',
      redirect: '/home',
      component: Layout,
      children: [...routes],
    },
  ],
})

// 全局前置守卫：设置页面标题
router.beforeEach((to, _from, next) => {
  // 获取页面标题
  let title = '小工单'  // 默认标题

  // 从路由匹配的记录中查找标题，优先使用最具体的路由标题
  const matched = to.matched
  for (let i = matched.length - 1; i >= 0; i--) {
    const route = matched[i]
    if (route.meta && route.meta.title) {
      title = `${route.meta.title} - 小工单`
      break
    }
  }

  // 设置页面标题
  document.title = title

  next()
})

export default router
