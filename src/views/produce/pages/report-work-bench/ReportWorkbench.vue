<template>
  <div class="report-workbench-container">
    <div class="report-workbench-header">
      <div class="report-workbench-title">报工工作台</div>
      <div class="report-workbench-user">
        <a-avatar>测</a-avatar>
        <span>测试</span>
      </div>
    </div>
    <div class="report-workbench-content">
      <!-- 左侧工作列表区域 -->
      <div class="work-list-section">
        <div class="section-header">
          <h3>今日报工</h3>
        </div>
        <div class="work-table-content">
          <a-table
            :columns="workColumns"
            :data-source="workList"
            :pagination="false"
            :customRow="customRow"
            :scroll="{ x: 700 }"
            bordered
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'status'">
                <a-tag :color="getStatusColor(record.status)">{{ record.status }}</a-tag>
              </template>
            </template>
          </a-table>
        </div>
      </div>
      <!-- 右侧信息填入区域 -->
      <div class="report-form-section">
        <div class="section-header">
          <span>快速报工</span>
        </div>
        <div class="report-form-content">
          <a-form :model="reportForm" :rules="rules" layout="vertical" ref="formRef">
            <a-form-item label="工单编号" name="workNumber" required>
              <a-select v-model:value="reportForm.workNumber" placeholder="请选择工单编号">
                <a-select-option value="WO202501001">WO202501001</a-select-option>
                <a-select-option value="WO202501002">WO202501002</a-select-option>
                <a-select-option value="WO202501003">WO202501003</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="工序名称" name="process" required>
              <a-select v-model:value="reportForm.process" placeholder="请选择工序名称">
                <a-select-option value="车削加工">车削加工</a-select-option>
                <a-select-option value="铣削加工">铣削加工</a-select-option>
                <a-select-option value="装配">装配</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="生产人员" name="operator" required>
              <a-select v-model:value="reportForm.operator" placeholder="请选择生产人员">
                <a-select-option value="张三">张三</a-select-option>
                <a-select-option value="李四">李四</a-select-option>
                <a-select-option value="王五">王五</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="良品数" name="goodCount" required>
              <a-input-number
                v-model:value="reportForm.goodCount"
                :min="0"
                :max="reportForm.totalQuantity"
                placeholder="请输入良品数"
                style="width: 100%"
              />
            </a-form-item>

            <a-form-item label="不良品数" name="badCount">
              <a-input-number
                v-model:value="reportForm.badCount"
                :min="0"
                placeholder="请输入不良数"
                style="width: 100%"
              />
            </a-form-item>

            <a-form-item label="单位" name="unit" required>
              <a-select v-model:value="reportForm.unit" placeholder="请选择单位">
                <a-select-option value="件">件</a-select-option>
                <a-select-option value="个">个</a-select-option>
                <a-select-option value="套">套</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="报工时长">
              <div class="time-inputs">
                <a-input-number
                  v-model:value="reportForm.workHours"
                  placeholder="0"
                  :min="0"
                  style="flex: 1"
                />
                <span>小时</span>
                <a-input-number
                  v-model:value="reportForm.workMinutes"
                  placeholder="0"
                  :min="0"
                  :max="59"
                  style="flex: 1"
                />
                <span>分钟</span>
              </div>
            </a-form-item>

            <a-form-item label="开始时间" name="startTime">
              <a-date-picker
                v-model:value="reportForm.startTime"
                show-time
                format="YYYY-MM-DD HH:mm"
                style="width: 100%"
              />
            </a-form-item>

            <a-form-item label="结束时间" name="endTime">
              <a-date-picker
                v-model:value="reportForm.endTime"
                show-time
                format="YYYY-MM-DD HH:mm"
                style="width: 100%"
              />
            </a-form-item>

            <a-form-item label="计价方式">
              <switch-tag v-model="reportForm.priceType" />
            </a-form-item>
          </a-form>
        </div>

        <div class="report-form-footer">
          <a-button size="large" @click="resetForm">清空</a-button>
          <a-button type="primary" @click="submitReport" size="large" style="flex: 1">
            提交报工
          </a-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { message } from 'ant-design-vue'
import type { TableColumnsType, FormInstance } from 'ant-design-vue'
import SwitchTag from './components/switch-tag.vue'

// 工作列表数据类型
interface WorkItem {
  id: string
  workNumber: string
  productName: string
  process: string
  quantity: number
  status: string
}

// 报工表单数据类型
interface ReportForm {
  workNumber: string
  productName: string
  process: string
  operator: string
  goodCount: number | null
  badCount: number | null
  workHours: number | null
  workMinutes: number | null
  unit: string
  startTime: any
  endTime: any
  totalQuantity: number
  priceType: string
}

// 表格列定义
const workColumns: TableColumnsType<WorkItem> = [
  {
    title: '工单编号',
    dataIndex: 'workNumber',
    key: 'workNumber',
    width: 120,
    fixed: 'left',
  },
  {
    title: '产品名称',
    dataIndex: 'productName',
    key: 'productName',
    width: 120,
  },
  {
    title: '工序名称',
    dataIndex: 'process',
    key: 'process',
    width: 100,
  },
  {
    title: '生产人员',
    dataIndex: 'worker',
    key: 'worker',
    width: 100,
  },
  {
    title: '良品数',
    dataIndex: 'goodCount',
    key: 'goodCount',
    width: 80,
  },
  {
    title: '不良品数',
    dataIndex: 'badCount',
    key: 'badCount',
    width: 80,
  },
  {
    title: '报工创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 180,
  },
]

// 模拟工作列表数据
const workList = ref<WorkItem[]>([
  {
    id: '1',
    workNumber: 'WO202501001',
    productName: '精密零件A',
    process: '车削加工',
    quantity: 100,
    status: '进行中',
  },
  {
    id: '2',
    workNumber: 'WO202501002',
    productName: '机械部件B',
    process: '铣削加工',
    quantity: 50,
    status: '待开始',
  },
  {
    id: '3',
    workNumber: 'WO202501003',
    productName: '装配件C',
    process: '装配',
    quantity: 200,
    status: '进行中',
  },
])

// 报工表单数据
const reportForm = reactive<ReportForm>({
  workNumber: '',
  productName: '',
  process: '',
  operator: '',
  goodCount: null,
  badCount: null,
  workHours: null,
  workMinutes: null,
  unit: '',
  startTime: null,
  endTime: null,
  totalQuantity: 0,
  priceType: 'piece',
})

// 表单验证规则
const rules = {
  workNumber: [{ required: true, message: '请选择工单编号', trigger: 'change' }],
  process: [{ required: true, message: '请选择工序名称', trigger: 'change' }],
  operator: [{ required: true, message: '请选择生产人员', trigger: 'change' }],
  unit: [{ required: true, message: '请选择单位', trigger: 'change' }],
}

// 表单引用
const formRef = ref<FormInstance>()

const customRow = () => {
  return {
    style: {
      height: '64px',
    },
  }
}

// 获取状态颜色
const getStatusColor = (status: string) => {
  switch (status) {
    case '进行中':
      return 'processing'
    case '待开始':
      return 'default'
    case '已完成':
      return 'success'
    default:
      return 'default'
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(reportForm, {
    workNumber: '',
    productName: '',
    process: '',
    operator: '',
    goodCount: null,
    badCount: null,
    workHours: null,
    workMinutes: null,
    unit: '',
    startTime: null,
    endTime: null,
    totalQuantity: 0,
  })
  // 重置表单验证状态
  formRef.value?.resetFields()
}

// 提交报工
const submitReport = async () => {
  try {
    await formRef.value?.validate()

    // 这里应该调用API提交数据
    console.log('提交报工数据:', reportForm)
    message.success('报工提交成功')

    // 提交成功后重置表单
    resetForm()
  } catch (error) {
    console.log('表单验证失败:', error)
    message.warning('请完善必填信息')
  }
}
</script>

<style scoped lang="scss">
.report-workbench-container {
  height: 100vh; // 全屏高度
  width: 100vw; // 全屏宽度
  margin: 0;
  padding: 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .report-workbench-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 20px;
    border-bottom: 1px solid #e5e5e5;
    flex-shrink: 0; // 防止头部被压缩

    div.report-workbench-title {
      color: #1b1b1b;
      font-size: 20px;
      font-weight: 500;
      line-height: 20px;
    }

    .report-workbench-user {
      display: flex;
      align-items: center;
      gap: 4px;
    }
  }

  .report-workbench-content {
    display: flex;
    flex: 1; // 占据剩余空间
    min-height: 0; // 确保flex子元素可以收缩
  }
}

.work-list-section {
  flex: 1;
  min-width: 600px;
  max-width: 100%;
  margin: 0 20px;
  position: relative;
  overflow: auto;

  .section-header {
    color: #1b1b1b;
    padding-top: 24px;
    padding-bottom: 16px;
    font-size: 20px;
    font-weight: 500;
    line-height: 20px;
  }

  .work-table-content {
    flex: 1;
    overflow: hidden;
    background: #fff;

    :deep(.ant-table-wrapper) {
      height: 100%;

      .ant-table {
        height: 100%;
      }

      .ant-table-tbody > tr {
        cursor: pointer;

        &:hover {
          background-color: #f5f5f5;
        }

        &.ant-table-row-selected {
          background-color: #e6f7ff;
        }
      }
    }
  }
}

.report-form-section {
  border-left: 1px solid #e5e5e5;
  width: 35%;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  position: relative;
  display: flex;
  flex-direction: column;
  background: #fff;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 14px 24px;
    flex-shrink: 0; // 防止头部被压缩
    border-bottom: 1px solid #e8e8e8;

    span {
      color: #1b1b1b;
      white-space: nowrap;
      text-overflow: ellipsis;
      word-break: break-all;
      width: 80px;
      padding-top: 10px;
      padding-bottom: 10px;
      font-size: 20px;
      font-weight: 500;
      line-height: 1;
      overflow: hidden;
    }
  }

  .report-form-content {
    flex: 1;
    overflow-y: auto;
    padding: 24px;
    min-height: 0; // 确保flex子元素可以收缩

    .ant-form {
      max-width: 600px;
    }

    .ant-form-item {
      margin-bottom: 12px;
    }
  }

  .report-form-footer {
    display: flex;
    justify-content: flex-end;
    padding: 16px 24px;
    border-top: 1px solid #e8e8e8;
    gap: 12px;
  }
}

.time-inputs {
  display: flex;
  align-items: center;
  gap: 12px;
}

// 响应式设计
@media (max-width: 1200px) {
  .work-list-section {
    width: 35%;
  }

  .report-form-section {
    width: 65%;
  }
}

@media (max-width: 768px) {
  .report-workbench-container {
    flex-direction: column;
  }

  .work-list-section {
    width: 100%;
    height: 40%;
  }

  .report-form-section {
    width: 100%;
    height: 60%;
  }
}
</style>
