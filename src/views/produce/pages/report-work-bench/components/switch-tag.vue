<template>
  <div class="switch-tag-container">
    <div class="switch-tag-content">
      <div
        class="tag-option"
        :class="{ active: activeValue === 'piece' }"
        @click="handleSwitch('piece')"
      >
        计件
      </div>
      <div
        class="tag-option"
        :class="{ active: activeValue === 'hour' }"
        @click="handleSwitch('hour')"
      >
        计时
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

interface Props {
  modelValue?: string
  defaultValue?: string
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: 'piece',
  defaultValue: 'piece',
})

const emit = defineEmits<{
  'update:modelValue': [value: string]
  change: [value: string]
}>()

const activeValue = ref(props.modelValue || props.defaultValue)

const handleSwitch = (value: string) => {
  activeValue.value = value
  emit('update:modelValue', value)
  emit('change', value)
}
</script>

<style scoped lang="scss">
.switch-tag-container {
  background: #ffffff;
  border-radius: 8px;

  .switch-tag-content {
    display: flex;
    background: #f5f5f5;
    border-radius: 6px;
    padding: 2px;

    .tag-option {
      flex: 1;
      padding: 8px 16px;
      border-radius: 4px;
      font-size: 14px;
      color: #666;
      cursor: pointer;
      justify-content: center;
      align-items: center;
      transition: all 0.3s ease-in-out;
      display: flex;

      &:hover {
        color: #333;
      }

      &.active {
        background-color: #fff;
        color: $primary-color;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
      }
    }
  }
}
</style>
