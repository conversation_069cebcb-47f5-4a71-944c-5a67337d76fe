import type { TableColumn } from '@/common/types'

export const tableColumns: TableColumn[] = [
  {
    title: '工单编号',
    dataIndex: 'workOrderNo',
    key: 'workOrderNo',
    width: 120,
    visible: true,
  },
  {
    title: '产品编号',
    dataIndex: 'productNo',
    key: 'productNo',
    width: 120,
    visible: true,
  },
  {
    title: '产品名称',
    dataIndex: 'productName',
    key: 'productName',
    width: 120,
    visible: true,
  },
  {
    title: '产品规格',
    dataIndex: 'productSpec',
    key: 'productSpec',
    width: 120,
    visible: false,
  },
  {
    title: '工单单位',
    dataIndex: 'workOrderUnit',
    key: 'workOrderUnit',
    width: 120,
    visible: false,
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 120,
    visible: false,
  },
  {
    title: '计划数',
    dataIndex: 'plannedQuantity',
    key: 'plannedQuantity',
    width: 120,
    visible: true,
  },
  {
    title: '良品数',
    dataIndex: 'qualifiedQuantity',
    key: 'qualifiedQuantity',
    width: 120,
    visible: false,
  },
  {
    title: '不良品数',
    dataIndex: 'defectiveQuantity',
    key: 'defectiveQuantity',
    width: 120,
    visible: false,
  },
  {
    title: '备注（工单）',
    dataIndex: 'workOrderRemark',
    key: 'workOrderRemark',
    width: 120,
    visible: false,
  },
  {
    title: '优先级',
    dataIndex: 'priority',
    key: 'priority',
    width: 120,
    visible: false,
  },
  {
    title: '关联业务单据',
    dataIndex: 'relatedBusinessDoc',
    key: 'relatedBusinessDoc',
    width: 120,
    visible: false,
  },
  {
    title: '关联销售订单',
    dataIndex: 'relatedSalesOrder',
    key: 'relatedSalesOrder',
    width: 120,
    visible: false,
  },
  {
    title: '计划开始时间',
    dataIndex: 'plannedStartTime',
    key: 'plannedStartTime',
    width: 120,
    visible: false,
  },
  {
    title: '计划结束时间',
    dataIndex: 'plannedEndTime',
    key: 'plannedEndTime',
    width: 120,
    visible: false,
  },
  {
    title: '实际开始时间',
    dataIndex: 'actualStartTime',
    key: 'actualStartTime',
    width: 120,
    visible: false,
  },
  {
    title: '实际结束时间',
    dataIndex: 'actualEndTime',
    key: 'actualEndTime',
    width: 120,
    visible: false,
  },
  {
    title: '用料序号',
    dataIndex: 'materialSeqNo',
    key: 'materialSeqNo',
    width: 120,
    visible: false,
  },
  {
    title: '子项产品编号',
    dataIndex: 'subProductNo',
    key: 'subProductNo',
    width: 120,
    visible: true,
  },
  {
    title: '子项产品名称',
    dataIndex: 'subProductName',
    key: 'subProductName',
    width: 120,
    visible: true,
  },
  {
    title: '子项产品规格',
    dataIndex: 'subProductSpec',
    key: 'subProductSpec',
    width: 120,
    visible: true,
  },
  {
    title: '子项单位',
    dataIndex: 'subUnit',
    key: 'subUnit',
    width: 120,
    visible: true,
  },
  {
    title: '子项产品属性',
    dataIndex: 'subProductAttribute',
    key: 'subProductAttribute',
    width: 120,
    visible: false,
  },
  {
    title: '投料工序',
    dataIndex: 'feedingProcess',
    key: 'feedingProcess',
    width: 120,
    visible: true,
  },
  {
    title: '投料工序编号',
    dataIndex: 'feedingProcessNo',
    key: 'feedingProcessNo',
    width: 120,
    visible: false,
  },
  {
    title: '单位用量',
    dataIndex: 'unitUsage',
    key: 'unitUsage',
    width: 120,
    visible: true,
  },
  {
    title: '需求数量',
    dataIndex: 'requiredQuantity',
    key: 'requiredQuantity',
    width: 120,
    visible: true,
  },
  {
    title: '子项库存数量',
    dataIndex: 'subInventoryQuantity',
    key: 'subInventoryQuantity',
    width: 120,
    visible: true,
  },
  {
    title: '未领料数量',
    dataIndex: 'unrequisitionedQuantity',
    key: 'unrequisitionedQuantity',
    width: 120,
    visible: true,
  },
  {
    title: '实际用料数量',
    dataIndex: 'actualUsageQuantity',
    key: 'actualUsageQuantity',
    width: 120,
    visible: true,
  },
  {
    title: '已领料数量',
    dataIndex: 'requisitionedQuantity',
    key: 'requisitionedQuantity',
    width: 120,
    visible: true,
  },
  {
    title: '已退料数量',
    dataIndex: 'returnedQuantity',
    key: 'returnedQuantity',
    width: 120,
    visible: true,
  },
  {
    title: '备注',
    dataIndex: 'remark',
    key: 'remark',
    width: 120,
    visible: false,
  },
  {
    title: '创建人（工单）',
    dataIndex: 'workOrderCreator',
    key: 'workOrderCreator',
    width: 120,
    visible: true,
  },
  {
    title: '创建时间（工单）',
    dataIndex: 'workOrderCreateTime',
    key: 'workOrderCreateTime',
    width: 120,
    visible: true,
  },
  {
    title: '更新人（工单）',
    dataIndex: 'workOrderUpdater',
    key: 'workOrderUpdater',
    width: 120,
    visible: false,
  },
  {
    title: '更新时间（工单）',
    dataIndex: 'workOrderUpdateTime',
    key: 'workOrderUpdateTime',
    width: 120,
    visible: false,
  },
  {
    title: '单价',
    dataIndex: 'unitPrice',
    key: 'unitPrice',
    width: 120,
    visible: false,
  },
  {
    title: '损耗',
    dataIndex: 'wastage',
    key: 'wastage',
    width: 120,
    visible: false,
  },
  {
    title: '创建人',
    dataIndex: 'creator',
    key: 'creator',
    width: 120,
    visible: true,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 120,
    visible: true,
  },
  {
    title: '更新人',
    dataIndex: 'updater',
    key: 'updater',
    width: 120,
    visible: false,
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    key: 'updateTime',
    width: 120,
    visible: false,
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    width: 120,
    visible: true,
    fixed: 'right',
  },
]