<template>
  <div class="material-list-page">
    <div class="list-action-wrapper">
      <QuickSearchTabbar :tabs="tabsData" @tab-click="handleTabClick" />
      <div class="search-row">
        <div>
          <SecondaryGroupingSelector :items="groupingItems" @change="handleGroupingChange" />
        </div>
        <div>
          <AdvancedSearchInput
            v-model="searchQuery"
            :search-options="searchOptions"
            @search="handleSearch"
            @type-change="handleSearchTypeChange"
          />
        </div>
      </div>
      <div class="filter-row">
        <AdvancedSearchForm ref="formRef" :collapsed-items="3">
          <template #item-1>
            <a-form-item name="workOrderNo" label="计划结束时间">
              <a-input placeholder="请输入工单编号" />
            </a-form-item>
          </template>
          <template #item-2>
            <a-form-item name="workOrderNo" label="计划结束时间">
              <a-input placeholder="请输入工单编号" />
            </a-form-item>
          </template>
          <template #item-3>
            <a-form-item name="workOrderNo" label="计划结束时间">
              <a-input placeholder="请输入工单编号" />
            </a-form-item>
          </template>
          <template #item-4>
            <a-form-item name="workOrderNo" label="计划结束时间">
              <a-input placeholder="请输入工单编号" />
            </a-form-item>
          </template>
          <template #item-5>
            <a-form-item name="workOrderNo" label="计划结束时间">
              <a-input placeholder="请输入工单编号" />
            </a-form-item>
          </template>
        </AdvancedSearchForm>
      </div>
    </div>
    <div class="list-table-wrapper">
      <ConfigurableTable
        :columns="tableColumns"
        :show-filter="false"
        :row-selection="{ type: 'checkbox' }"
        :dataSource="dataSource"
      >
        <template #toolbar-left>
          <a-button type="default" :icon="h(UploadOutlined)">导出</a-button>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <a-space>
              <a @click="goToEditMaterial(record.id)">编辑</a>
              <a>删除</a>
            </a-space>
          </template>
        </template>
      </ConfigurableTable>
    </div>
  </div>
  <EditMaterial
    :open="showEditMaterial"
    :id="editMaterialId"
    @confirm="handleEditMaterial"
    @cancel="handleEditMaterialCancel"
  />
</template>

<script setup lang="ts">
import { ref, h } from 'vue'
import { ProfileFilled, UploadOutlined } from '@ant-design/icons-vue'

import { tableColumns } from './columns'

import QuickSearchTabbar from '@/components/QuickSearchTabbar/QuickSearchTabbar.vue'
import SecondaryGroupingSelector from '@/components/SecondaryGroupingSelector.vue'
import AdvancedSearchInput from '@/components/AdvancedSearchInput.vue'
import AdvancedSearchForm from '@/components/AdvancedSearchForm/index.vue'
import ConfigurableTable from '@/components/ConfigurableTable/index.vue'
import EditMaterial from './components/edit-material.vue'

// 定义标签数据
const tabsData = ref([
  { name: '全部', icon: ProfileFilled, active: true },
  { name: '看板1', icon: ProfileFilled, active: false },
  { name: '看板2', icon: ProfileFilled, active: false },
])
// 定义分组数据
const groupingItems = ref([
  { label: '全部', value: 'all' },
  { label: '压铸', value: 'casting' },
  { label: '去毛刺', value: 'deburring' },
  { label: '车', value: 'turning' },
  { label: '钻', value: 'drilling' },
  { label: '铣', value: 'milling' },
  { label: '氧化', value: 'oxidation' },
  { label: '喷涂', value: 'painting' },
  { label: '包装入库', value: 'packaging' },
])
const searchQuery = ref('')
const searchOptions = ref([
  {
    label: '工单编号、产品编号、产品名称、产品规格',
    value: 'all',
    placeholder: '输入产品编号搜索',
  },
  { label: '工单编号', value: 'workOrderNo', placeholder: '输入工单编号搜索' },
  { label: '产品编号', value: 'productNo', placeholder: '输入产品编号搜索' },
  { label: '产品名称', value: 'productName', placeholder: '输入产品名称搜索' },
  { label: '产品规格', value: 'productSpec', placeholder: '输入产品规格搜索' },
  { label: '工单备注', value: 'workOrderNote', placeholder: '输入工单备注搜索' },
  { label: '紧急程度', value: 'urgency', placeholder: '输入紧急程度搜索' },
  { label: '压铸机', value: 'machine', placeholder: '输入压铸机搜索' },
])
const dataSource = ref([
  {
    id: 1,
    productNo: '**********',
    productName: '产品1',
    productSpec: '规格1',
    productAttribute: '自制',
    processRoute: '标准工艺',
    defaultSupplier: '供应商A',
    maxStock: 100,
    minStock: 10,
  },
])
const showEditMaterial = ref(false)
const editMaterialId = ref('')

// 事件处理函数
const handleTabClick = (tab: any, index: number) => {
  tabsData.value.forEach((item, i) => {
    item.active = i === index
  })
  console.log('Tab clicked:', tab, index)
}

const handleGroupingChange = (value: any) => {
  console.log('Grouping changed:', value)
}

const handleSearch = (data: { type: string; value: string }) => {
  console.log('Search triggered:', data)
  // 这里可以调用API进行搜索
}

const handleSearchTypeChange = (type: string) => {
  console.log('Search type changed:', type)
}

const goToEditMaterial = (id: string) => {
  showEditMaterial.value = true
  editMaterialId.value = id
}

const handleEditMaterial = (data: any) => {
  console.log('Edit material:', data)
}

const handleEditMaterialCancel = () => {
  showEditMaterial.value = false
}
</script>

<style lang="scss" scoped>
.material-list-page {
  height: 100%;
  overflow: auto;
}

.search-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 24px;
  margin-top: $spacing-base;
  padding: 0 $spacing-lg;
}

.filter-row {
  margin-top: $spacing-base;
  padding: 0 $spacing-lg;
}

.list-table-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  margin-top: $spacing-md;
  padding: 0 $spacing-lg $spacing-md;
}
</style>
