<template>
  <a-modal
    width="528px"
    centered
    wrapClassName="m-modal"
    title="编辑用料"
    ok-text="确定"
    cancel-text="取消"
    :open="props.open"
    :body-style="{
      minHeight: '320px',
      maxHeight: 'min(-114px + 94vh, 960px)',
      overflowY: 'auto',
      padding: '12px 24px',
    }"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <a-form ref="formRef" class="edit-material-form" :model="formData" :rules="formRules" layout="vertical">
      <!-- 产品信息 -->
      <a-form-item label="产品信息" name="productInfo">
        <a-select
          v-model:value="formData.productInfo"
          placeholder="请选择产品"
          show-search
          :filter-option="filterProductOption"
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
        >
          <a-select-option
            v-for="product in productOptions"
            :key="product.value"
            :value="product.value"
          >
          </a-select-option>
        </a-select>
        <template #extra>
          <ProductDetail :product-info="{}" />
        </template>
      </a-form-item>

      <!-- 单位用量 -->
      <a-form-item label="单位用量" name="unitUsage">
        <a-input-number
          v-model:value="formData.unitUsage"
          :min="0"
          :precision="4"
          :step="0.0001"
          style="width: 100%"
          placeholder="请输入单位用量"
        />
      </a-form-item>

      <a-form-item label="投料工序" name="feedingProcess">
        <a-select v-model:value="formData.feedingProcess" placeholder="请选择投料工序" allowClear>
          <a-select-option
            v-for="process in processOptions"
            :key="process.value"
            :value="process.value"
          >
            {{ process.label }}
          </a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="备注" name="remark">
        <a-textarea
          v-model:value="formData.remark"
          placeholder="请输入"
          :rows="4"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, watch, computed } from 'vue'
import type { FormInstance } from 'ant-design-vue'
import { message } from 'ant-design-vue'

import ProductDetail from '@/components/ProductDetail/index.vue'

const props = withDefaults(
  defineProps<{
    open: boolean
    id: string | number
  }>(),
  {
    open: false,
  },
)
const emit = defineEmits(['confirm', 'cancel'])

const formRef = ref<FormInstance>()

// 表单数据
const formData = ref({
  productInfo: '',
  unitUsage: undefined as number | undefined,
  feedingProcess: '',
  remark: '',
})

// 表单验证规则
const formRules = {
  productInfo: [{ required: true, message: '请选择产品信息', trigger: 'change' }],
  unitUsage: [
    { required: true, message: '请输入单位用量', trigger: 'blur' },
    { type: 'number', min: 0, message: '单位用量不能小于0', trigger: 'blur' },
  ],
}

// 产品选项（模拟数据）
const productOptions = ref([
  {
    value: '**********',
    code: '**********',
    name: '铝锭',
    spec: 'ADC12',
    inventory: -34579.3675,
  },
  {
    value: 'CP20230007',
    code: 'CP20230007',
    name: '铜锭',
    spec: 'T2',
    inventory: 15200.5,
  },
  {
    value: 'CP20230008',
    code: 'CP20230008',
    name: '锌锭',
    spec: 'SHG',
    inventory: 8900.25,
  },
])

// 投料工序选项
const processOptions = ref([
  { label: '压铸', value: 'casting' },
  { label: '去毛刺', value: 'deburring' },
  { label: '车', value: 'turning' },
  { label: '钻', value: 'drilling' },
  { label: '铣', value: 'milling' },
  { label: '氧化', value: 'oxidation' },
  { label: '喷涂', value: 'painting' },
  { label: '包装入库', value: 'packaging' },
])

// 选中的产品信息
const selectedProduct = computed(() => {
  if (!formData.value.productInfo) return null
  return productOptions.value.find((p) => p.value === formData.value.productInfo)
})

// 产品选项过滤
const filterProductOption = (input: string, option: any) => {
  const product = productOptions.value.find((p) => p.value === option.value)
  if (!product) return false

  const searchText = input.toLowerCase()
  return (
    product.code.toLowerCase().includes(searchText) ||
    product.name.toLowerCase().includes(searchText) ||
    product.spec.toLowerCase().includes(searchText)
  )
}

// 确认按钮点击事件
const handleOk = async () => {
  try {
    await formRef.value?.validate()
    const formDataValue = formRef.value?.getFieldsValue()

    // TODO: 调用接口
    message.success('用料编辑成功！')
    emit('confirm', formDataValue)
  } catch (error) {
    console.log('表单验证失败:', error)
    message.error('请检查表单填写是否正确')
  }
}

// 取消按钮点击事件
const handleCancel = () => {
  emit('cancel', false)
}

// 重置表单
const resetForm = () => {
  formData.value = {
    productInfo: '',
    unitUsage: undefined as number | undefined,
    feedingProcess: '',
    remark: '',
  }
  formRef.value?.resetFields()
}

// 监听弹窗打开，加载数据
watch(
  () => props.open,
  (newVal: boolean) => {
    if (newVal) {
      // TODO: 调用接口加载数据
      // 如果是编辑模式，根据props.id加载对应的数据
      if (props.id) {
        // 模拟加载编辑数据
        formData.value = {
          productInfo: '**********',
          unitUsage: 2.5725,
          feedingProcess: 'casting',
          remark: '',
        }
      } else {
        resetForm()
      }
    } else {
      resetForm()
    }
  },
)
</script>

<style lang="scss" scoped></style>
