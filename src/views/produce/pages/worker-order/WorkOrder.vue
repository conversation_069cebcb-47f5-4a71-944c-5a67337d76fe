<template>
  <div class="work-order-page">
    <div class="list-action-wrapper">
      <QuickSearchTabbar :tabs="tabsData" :columns="tableColumns" @tab-click="handleTabClick" />
      <div class="search-row">
        <div>
          <SecondaryGroupingSelector :items="groupingItems" @change="handleGroupingChange" />
        </div>
        <div>
          <AdvancedSearchInput
            v-model="searchQuery"
            :search-options="searchOptions"
            @search="handleSearch"
            @type-change="handleSearchTypeChange"
          />
        </div>
      </div>
    </div>
    <div class="list-table-wrapper">
      <ConfigurableTable
        :columns="tableColumns"
        :row-selection="{ type: 'checkbox' }"
        :dataSource="dataSource"
        @line-height-change="handleChangeLineHeight"
      >
        <template #toolbar-left>
          <a-button type="primary" :icon="h(PlusCircleFilled)" @click="openWorkerOrder()">
            创建工单
          </a-button>
          <a-button type="default" :icon="h(UploadOutlined)">导出</a-button>
          <a-button type="default" :icon="h(DownloadOutlined)">导入</a-button>
        </template>
        <template #toolbar-right>
          <a-button type="text" class="custom-btn">
            <svg-icon icon-name="icon-log" color="#000" />
            <span>导入日志</span>
          </a-button>
          <a-button type="text" class="custom-btn">
            <svg-icon icon-name="icon-jilu" color="#000" />
            <span>操作记录</span>
          </a-button>
        </template>
        <template #selection-toolbar-left>
          <a-button type="default" :icon="h(UploadOutlined)">导出</a-button>
          <a-button type="default" :icon="h(UploadOutlined)">开始</a-button>
          <a-button type="default" :icon="h(UploadOutlined)">打印</a-button>
          <a-button type="default" :icon="h(UploadOutlined)">优先级</a-button>
          <a-button type="default" :icon="h(UploadOutlined)">修改</a-button>
          <a-button type="default" :icon="h(UploadOutlined)">报工</a-button>
          <a-button type="default" danger :icon="h(DeleteOutlined)">删除</a-button>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <CustomTag
              :color="getStatusInfo(record.status).color"
              :text="getStatusInfo(record.status).text"
            />
          </template>
          <template v-if="column.key === 'progressBar'">
            <ProgressBar :size="lineHeight === 'low' ? 'small' : 'default'" :progress-list="step" />
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a @click="openWorkerOrder('edit', record.id)">编辑</a>
              <a @click="openWorkerOrder('view', record.id)">查看</a>
              <a>操作</a>
            </a-space>
          </template>
        </template>
      </ConfigurableTable>
    </div>
  </div>
  <WorkerOrderItem :open="open" :orderId="orderId" :type="orderType" @close="onClose" />
</template>

<script setup lang="ts">
import { ref, h } from 'vue'
import {
  ProfileFilled,
  PlusCircleFilled,
  UploadOutlined,
  DownloadOutlined,
  DeleteOutlined,
} from '@ant-design/icons-vue'

import QuickSearchTabbar from '@/components/QuickSearchTabbar/QuickSearchTabbar.vue' // 快速搜索相关
import SecondaryGroupingSelector from '@/components/SecondaryGroupingSelector.vue' // 二级分组选择器
import AdvancedSearchInput from '@/components/AdvancedSearchInput.vue' // 高级搜索输入框
import ConfigurableTable from '@/components/ConfigurableTable/index.vue'
import CustomTag from '@/components/CustomTag/index.vue'
import ProgressBar, { type ProgressItem } from '@/components/ProgressBar/index.vue'
import WorkerOrderItem from './components/worker-order-item/worker-order-item.vue'

import { tableColumns } from './tableColumns'
import { mockData } from './mockData'

const tabsData = ref([
  { name: '全部', icon: ProfileFilled, active: true },
  { name: '看板1', icon: ProfileFilled, active: false },
  { name: '看板2', icon: ProfileFilled, active: false },
])
const groupingItems = ref([
  { label: '全部', value: 'all' },
  { label: '压铸', value: 'casting' },
  { label: '去毛刺', value: 'deburring' },
  { label: '车', value: 'turning' },
  { label: '钻', value: 'drilling' },
  { label: '铣', value: 'milling' },
  { label: '氧化', value: 'oxidation' },
  { label: '喷涂', value: 'painting' },
  { label: '包装入库', value: 'packaging' },
])
const searchQuery = ref('')
const searchOptions = ref([
  {
    label: '工单编号、产品编号、产品名称、产品规格',
    value: 'all',
    placeholder: '输入产品编号搜索',
  },
  { label: '工单编号', value: 'workOrderNo', placeholder: '输入工单编号搜索' },
  { label: '产品编号', value: 'productNo', placeholder: '输入产品编号搜索' },
  { label: '产品名称', value: 'productName', placeholder: '输入产品名称搜索' },
  { label: '产品规格', value: 'productSpec', placeholder: '输入产品规格搜索' },
  { label: '工单备注', value: 'workOrderNote', placeholder: '输入工单备注搜索' },
  { label: '紧急程度', value: 'urgency', placeholder: '输入紧急程度搜索' },
  { label: '压铸机', value: 'machine', placeholder: '输入压铸机搜索' },
])
const lineHeight = ref<string>('')
const dataSource = ref(mockData)
const step = ref<ProgressItem[]>([
  { status: 'wait', percent: 0, stepName: '压铸' },
  { status: 'process', percent: 12, stepName: '去毛刺' },
  { status: 'process', percent: 48, stepName: '车' },
  { status: 'success', percent: 102, stepName: '钻' },
  { status: 'success', percent: 100, stepName: '铣' },
  { status: 'wait', percent: 8, stepName: '氧化' },
  { status: 'wait', percent: 1, stepName: '喷涂' },
])
const open = ref<boolean>(false)
const orderId = ref<string | number>('')
const orderType = ref<'create' | 'edit' | 'view'>('create')

// 事件处理函数
const handleTabClick = (tab: any, index: number) => {
  tabsData.value.forEach((item, i) => {
    item.active = i === index
  })
  console.log('Tab clicked:', tab, index)
}

// 定义分组数据
const handleGroupingChange = (value: any) => {
  console.log('Grouping changed:', value)
}

// 搜索相关数据
const handleSearchTypeChange = (type: string) => {
  console.log('Search type changed:', type)
}

const handleSearch = (data: { type: string; value: string }) => {
  console.log('Search triggered:', data)
  // 这里可以调用API进行搜索
}

const handleChangeLineHeight = (value: string) => {
  lineHeight.value = value
}

const getStatusInfo = (status: number) => {
  switch (status) {
    case 0:
      return { color: 'wait', text: '未开始' }
    case 1:
      return { color: 'processing', text: '进行中' }
    case 2:
      return { color: 'success', text: '已完成' }
    case 3:
      return { color: 'warn', text: '已取消' }
    default:
      return { color: '#333', text: '未知' }
  }
}

const openWorkerOrder = (type: 'create' | 'edit' | 'view' = 'create', id?: string | number) => {
  open.value = true
  orderType.value = type
  orderId.value = id || ''
}

const onClose = () => {
  open.value = false
}
</script>

<style src="./index.scss" scoped></style>
