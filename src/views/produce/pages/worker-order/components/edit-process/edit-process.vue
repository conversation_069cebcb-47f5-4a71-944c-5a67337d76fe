<template>
  <a-modal
    width="528px"
    centered
    wrapClassName="m-modal"
    title="编辑工序"
    ok-text="确定"
    cancel-text="取消"
    :open="props.open"
    :body-style="{
      minHeight: '320px',
      maxHeight: 'min(-114px + 94vh, 960px)',
      overflowY: 'auto',
      padding: '12px 24px',
    }"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      :label-col="{
        style: { width: '120px', height: '40px', lineHeight: '16px', whiteSpace: 'pre-wrap' },
      }"
      class="edit-process-form"
    >
      <a-form-item label="工序编号" name="processCode" required>
        <a-select v-model:value="formData.processCode" placeholder="请选择工序编号" allow-clear>
          <a-select-option value="**********">**********</a-select-option>
          <a-select-option value="**********">**********</a-select-option>
          <a-select-option value="**********">**********</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="工序名称" name="processName" required>
        <a-input v-model:value="formData.processName" disabled placeholder="请输入工序名称" />
      </a-form-item>

      <a-form-item label="报工权限" name="reportPermissions" required>
        <a-select
          v-model:value="formData.reportPermissions"
          mode="tags"
          disabled
          placeholder="请选择报工权限"
          :options="reportPermissionOptions.map((item) => ({ label: item, value: item }))"
          style="width: 100%"
        />
      </a-form-item>

      <a-form-item label="报工数配比" name="reportRatio" required>
        <a-input-number
          v-model:value="formData.reportRatio"
          :min="0"
          :max="100"
          placeholder="请输入报工数配比"
          style="width: 100%"
        />
      </a-form-item>

      <a-form-item label="不良品项列表" name="defectItems">
        <a-select
          v-model:value="formData.defectItems"
          mode="tags"
          disabled
          placeholder="请选择不良品项"
          :options="defectItemOptions.map((item) => ({ label: item, value: item }))"
          style="width: 100%"
        />
      </a-form-item>

      <a-form-item label="计划数" name="plannedQuantity" required>
        <a-input-number
          v-model:value="formData.plannedQuantity"
          :min="0"
          placeholder="请输入计划数"
          style="width: 100%"
        />
      </a-form-item>

      <a-form-item label="良品数" name="goodQuantity">
        <a-input-number
          v-model:value="formData.goodQuantity"
          :min="0"
          disabled
          style="width: 100%"
        />
      </a-form-item>

      <a-form-item label="不良品数" name="defectQuantity">
        <a-input-number
          v-model:value="formData.defectQuantity"
          :min="0"
          disabled
          style="width: 100%"
        />
      </a-form-item>

      <a-form-item label="计划开始/结束时间" name="plannedTime" required>
        <a-range-picker
          v-model:value="formData.plannedTime"
          show-time
          format="YYYY-MM-DD HH:mm"
          style="width: 100%"
        />
      </a-form-item>

      <a-form-item label="实际开始/结束时间" name="actualTime">
        <a-range-picker
          v-model:value="formData.actualTime"
          show-time
          format="YYYY-MM-DD HH:mm"
          style="width: 100%"
          disabled
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import type { FormInstance } from 'ant-design-vue'
import dayjs, { type Dayjs } from 'dayjs'

const props = defineProps<{
  open: boolean
}>()

const emit = defineEmits(['confirm', 'cancel'])

const formRef = ref<FormInstance>()

// 表单数据
const formData = reactive({
  processCode: '**********',
  processName: '压铸',
  reportPermissions: ['办公室', '压铸车间'],
  reportRatio: 1,
  defectItems: [
    '其他',
    '表面有花纹',
    '表面有细小凸痕',
    '表面有挫杆印痕',
    '表面有裂纹',
    '局部欠料',
    '纯角处理充不满',
    '型腔充填不满',
    '内有气孔产生',
    '结构松驰强度不够',
    '内含杂质',
  ],
  plannedQuantity: 20000,
  goodQuantity: 0,
  defectQuantity: 0,
  plannedTime: [dayjs('2025-07-24 00:00'), dayjs('2025-07-24 23:59')] as [Dayjs, Dayjs],
  actualTime: null as [Dayjs, Dayjs] | null,
  notes: '1. 穿戴劳保用品（安全帽，耳塞，工作服，手套，劳保鞋）；',
})

// 选项数据
const reportPermissionOptions = ['办公室', '压铸车间', '机加车间', '装配车间', '质检部', '仓库']
const defectItemOptions = [
  '其他',
  '表面有花纹',
  '表面有细小凸痕',
  '表面有挫杆印痕',
  '表面有裂纹',
  '表面有气孔',
  '局部欠料',
  '纯角处理充不满',
  '型腔充填不满',
  '内有气孔产生',
  '结构松驰强度不够',
  '内含杂质',
]

// 表单验证规则
const rules = {
  processCode: [{ required: true, message: '请选择工序编号', trigger: 'change' }],
  processName: [{ required: true, message: '请输入工序名称', trigger: 'blur' }],
  reportPermissions: [{ required: true, message: '请选择报工权限', trigger: 'change' }],
  reportRatio: [{ required: true, message: '请输入报工数配比', trigger: 'blur' }],
  plannedQuantity: [{ required: true, message: '请输入计划数', trigger: 'blur' }],
  plannedTime: [{ required: true, message: '请选择计划开始/结束时间', trigger: 'change' }],
}

const handleOk = async () => {
  try {
    await formRef.value?.validate()
    emit('confirm', formData)
  } catch (error) {
    console.log('表单验证失败:', error)
  }
}

const handleCancel = () => {
  emit('cancel', false)
}
</script>

<style lang="scss" scoped>
.edit-process-form {
  :deep(.ant-form-item-label) {
    font-weight: 500;
  }

  :deep(.ant-input-number) {
    width: 100%;
  }

  :deep(.ant-select-selector) {
    min-height: 32px;
  }
}
</style>
