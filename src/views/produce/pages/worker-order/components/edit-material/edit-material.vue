<template>
  <a-modal
    width="528px"
    centered
    wrapClassName="m-modal"
    title="编辑用料"
    ok-text="确定"
    cancel-text="取消"
    :open="props.open"
    :body-style="{
      minHeight: '320px',
      maxHeight: 'min(-114px + 94vh, 960px)',
      overflowY: 'auto',
      padding: '12px 24px',
    }"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="vertical"
      class="edit-material-form"
    >
      <a-form-item label="子项产品" name="productCode" required>
        <a-select v-model:value="formData.productCode" placeholder="请选择子项产品" allow-clear>
          <a-select-option value="**********">**********</a-select-option>
          <a-select-option value="**********">**********</a-select-option>
          <a-select-option value="**********">**********</a-select-option>
        </a-select>
        <ProductDetail :product-info="{}" />
      </a-form-item>

      <a-form-item label="单位用量" name="unitUsage" required>
        <a-input-number
          v-model:value="formData.unitUsage"
          :min="0"
          :max="100"
          placeholder="请输入单位用量"
          style="width: 100%"
        />
      </a-form-item>

      <a-form-item label="投料工序" name="processCode">
        <a-select
          v-model:value="formData.processCode"
          placeholder="请选择投料工序"
          style="width: 100%"
        >
          <a-select-option value="**********">**********</a-select-option>
          <a-select-option value="**********">**********</a-select-option>
          <a-select-option value="**********">**********</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="备注" name="remark">
        <a-textarea v-model:value="formData.remark" :rows="4" placeholder="请输入" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import type { FormInstance } from 'ant-design-vue'

import ProductDetail from '@/components/ProductDetail/index.vue'

const props = defineProps<{
  open: boolean
}>()

const emit = defineEmits(['confirm', 'cancel'])

const formRef = ref<FormInstance>()

// 表单数据
const formData = reactive({
  productCode: '',
  unitUsage: undefined,
  processCode: undefined,
  remark: '',
})

// 表单验证规则
const rules = {
  productCode: [{ required: true, message: '请选择子项产品', trigger: 'change' }],
  unitUsage: [{ required: true, message: '请输入单位用量', trigger: 'blur' }],
}

const handleOk = async () => {
  try {
    await formRef.value?.validate()
    emit('confirm', formData)
  } catch (error) {
    console.log('表单验证失败:', error)
  }
}

const handleCancel = () => {
  emit('cancel', false)
}
</script>

<style lang="scss" scoped>
.edit-material-form {
  :deep(.ant-form-item-label) {
    font-weight: 500;
  }

  :deep(.ant-input-number) {
    width: 100%;
  }

  :deep(.ant-select-selector) {
    min-height: 32px;
  }
}
</style>
