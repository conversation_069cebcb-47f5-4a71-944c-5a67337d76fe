<template>
  <div class="work-order-page">
    <div class="list-action-wrapper">
      <QuickSearchTabbar :tabs="tabsData" @tab-click="handleTabClick" />
      <div class="search-row">
        <div>
          <SecondaryGroupingSelector :items="groupingItems" @change="handleGroupingChange" />
        </div>
      </div>
      <div class="filter-row">
        <AdvancedSearchForm ref="workOrderFormRef" :collapsed-items="3">
          <template #item-1>
            <a-form-item name="workOrderNo" label="计划结束时间">
              <a-input placeholder="请输入工单编号" />
            </a-form-item>
          </template>
          <template #item-2>
            <a-form-item name="workOrderNo" label="计划结束时间">
              <a-input placeholder="请输入工单编号" />
            </a-form-item>
          </template>
          <template #item-3>
            <a-form-item name="workOrderNo" label="计划结束时间">
              <a-input placeholder="请输入工单编号" />
            </a-form-item>
          </template>
          <template #item-4>
            <a-form-item name="workOrderNo" label="计划结束时间">
              <a-input placeholder="请输入工单编号" />
            </a-form-item>
          </template>
          <template #item-5>
            <a-form-item name="workOrderNo" label="计划结束时间">
              <a-input placeholder="请输入工单编号" />
            </a-form-item>
          </template>
        </AdvancedSearchForm>
      </div>
    </div>
    <div class="list-table-wrapper">
      <ConfigurableTable
        :columns="tableColumns"
        :show-filter="false"
        :row-selection="{ type: 'checkbox' }"
        :dataSource="mockData"
      >
        <template #toolbar-left>
          <a-button type="primary" :icon="h(PlusCircleFilled)" @click="goToCreateReport">
            创建报工
          </a-button>
          <a-button type="default" :icon="h(UploadOutlined)">导出</a-button>
          <a-button type="default" :icon="h(DownloadOutlined)">导入</a-button>
        </template>
        <template #toolbar-right>
          <a-button type="text" class="custom-btn">
            <svg-icon icon-name="icon-log" color="#000" />
            <span>导入日志</span>
          </a-button>
          <a-button type="text" class="custom-btn">
            <svg-icon icon-name="icon-jilu" color="#000" />
            <span>操作记录</span>
          </a-button>
        </template>
        <template #selection-toolbar-left>
          <a-button type="default" :icon="h(UploadOutlined)">导出</a-button>
          <a-button type="default" :icon="h(UploadOutlined)">开始</a-button>
          <a-button type="default" :icon="h(UploadOutlined)">打印</a-button>
          <a-button type="default" :icon="h(UploadOutlined)">优先级</a-button>
          <a-button type="default" :icon="h(UploadOutlined)">修改</a-button>
          <a-button type="default" :icon="h(UploadOutlined)">报工</a-button>
          <a-button type="default" danger :icon="h(DeleteOutlined)">删除</a-button>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'workOrderNo'">
            <a @click="checkWorkerOrder(record.id)">{{ record.workOrderNo }}</a>
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a @click="goToEditReport(record.id)">编辑</a>
              <a @click="goToDetailReport(record.id)">查看</a>
              <a>操作</a>
            </a-space>
          </template>
        </template>
      </ConfigurableTable>
    </div>
  </div>
  <WorkerOrderItem
    :open="workerOrderItemOpen"
    :orderId="workerOrderItemId"
    @close="workerOrderItemOpen = false"
    type="edit"
  />
</template>

<script setup lang="ts">
import { ref, h } from 'vue'
import { useRouter } from 'vue-router'
import {
  ProfileFilled,
  PlusCircleFilled,
  UploadOutlined,
  DownloadOutlined,
  FilterOutlined,
  SortAscendingOutlined,
  DeleteOutlined,
} from '@ant-design/icons-vue'

import QuickSearchTabbar from '@/components/QuickSearchTabbar/QuickSearchTabbar.vue'
import SecondaryGroupingSelector from '@/components/SecondaryGroupingSelector.vue'
import AdvancedSearchForm from '@/components/AdvancedSearchForm/index.vue'
import ConfigurableTable from '@/components/ConfigurableTable/index.vue'
import WorkerOrderItem from '../../worker-order/components/worker-order-item/worker-order-item.vue'

import { tableColumns } from '../models/Report/columns'
import { mockData } from '../models/Report/mockData'

const router = useRouter()

// 定义标签数据
const tabsData = ref([
  { name: '全部', icon: ProfileFilled, active: true },
  { name: '看板1', icon: ProfileFilled, active: false },
  { name: '看板2', icon: ProfileFilled, active: false },
])
const groupingItems = ref([
  { label: '全部', value: 'all' },
  { label: '压铸', value: 'casting' },
  { label: '去毛刺', value: 'deburring' },
  { label: '车', value: 'turning' },
  { label: '钻', value: 'drilling' },
  { label: '铣', value: 'milling' },
  { label: '氧化', value: 'oxidation' },
  { label: '喷涂', value: 'painting' },
  { label: '包装入库', value: 'packaging' },
])
const workerOrderItemOpen = ref<boolean>(false)
const workerOrderItemId = ref<string>('')

// 跳转到创建报工页面
const goToCreateReport = () => {
  router.push('/produce/report/create')
}

// 跳转到编辑报工页面
const goToEditReport = (id: number) => {
  router.push(`/produce/report/edit/${id}`)
}

// 跳转到报工详情页面
const goToDetailReport = (id: number) => {
  router.push(`/produce/report/detail/${id}`)
}

// 查看工单相亲
const checkWorkerOrder = (id: string) => {
  workerOrderItemOpen.value = true
  workerOrderItemId.value = id
}

// 事件处理函数
const handleTabClick = (tab: any, index: number) => {
  tabsData.value.forEach((item, i) => {
    item.active = i === index
  })
  console.log('Tab clicked:', tab, index)
}

const handleGroupingChange = (value: any) => {
  console.log('Grouping changed:', value)
}
</script>

<style src="../models/Report/index.scss" scoped></style>
