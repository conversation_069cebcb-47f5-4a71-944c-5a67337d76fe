<template>
  <div class="add-report-container">
    <div class="form-container">
      <a-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        :label-col="{ style: { width: '100px' } }"
        @finish="onFinish"
        @finishFailed="onFinishFailed"
      >
        <!-- 基础信息 -->
        <h3 class="section-title">基础信息</h3>
        <div class="row-item">
          <div class="section">
            <a-form-item label="工单编号" name="workOrderNumber">
              <a-input v-model:value="formData.workOrderNumber" placeholder="请选择" />
            </a-form-item>

            <a-form-item label="工序名称" name="processName">
              <a-select v-model:value="formData.processName" placeholder="请选择">
                <a-select-option value="工序1">工序1</a-select-option>
                <a-select-option value="工序2">工序2</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="工序状态" name="processStatus">
              <a-radio-group v-model:value="formData.processStatus">
                <a-radio value="未开始">未开始</a-radio>
                <a-radio value="执行中">执行中</a-radio>
                <a-radio value="已结束">已结束</a-radio>
              </a-radio-group>
            </a-form-item>

            <a-form-item label="生产人员" name="worker">
              <a-select v-model:value="formData.worker" placeholder="请选择">
                <a-select-option value="张三">张三</a-select-option>
                <a-select-option value="李四">李四</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="报工数" name="workCount">
              <a-input v-model:value="formData.workCount" placeholder="请输入" />
            </a-form-item>

            <a-form-item label="单位" name="unit">
              <a-select v-model:value="formData.unit" placeholder="请选择">
                <a-select-option value="件">件</a-select-option>
                <a-select-option value="个">个</a-select-option>
                <a-select-option value="套">套</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="良品数" name="goodCount">
              <a-input v-model:value="formData.goodCount" placeholder="请选择" />
            </a-form-item>

            <a-form-item label="不良品数" name="defectCount">
              <a-input v-model:value="formData.defectCount" placeholder="请选择" />
            </a-form-item>

            <a-form-item label="不良品项" name="defectItems">
              <a-input v-model:value="formData.defectItems" placeholder="请选择" />
            </a-form-item>

            <a-form-item label="工序进度" name="processProgress">
              <a-input v-model:value="formData.processProgress" placeholder="" />
            </a-form-item>
          </div>

          <div class="section">
            <a-form-item label="产品编号" name="productNumber">
              <a-input v-model:value="formData.productNumber" placeholder="" />
            </a-form-item>

            <a-form-item label="产品名称" name="productName">
              <a-input v-model:value="formData.productName" placeholder="" />
            </a-form-item>

            <a-form-item label="产品规格" name="productSpec">
              <a-input v-model:value="formData.productSpec" placeholder="" />
            </a-form-item>

            <a-form-item label="开始时间" name="startTime">
              <a-date-picker
                v-model:value="formData.startTime"
                show-time
                format="YYYY-MM-DD HH:mm"
                placeholder="2025-07-28 14:00"
                style="width: 100%"
              />
            </a-form-item>

            <a-form-item label="结束时间" name="endTime">
              <a-date-picker
                v-model:value="formData.endTime"
                show-time
                format="YYYY-MM-DD HH:mm"
                placeholder="2025-07-28 14:00"
                style="width: 100%"
              />
            </a-form-item>

            <div class="work-time-input">
              <a-form-item label="报工时长">
                <div class="time-inputs">
                  <a-input-number
                    v-model:value="formData.workHours"
                    placeholder="0"
                    :min="0"
                    style="width: 80px"
                  />
                  <span>小时</span>
                  <a-input-number
                    v-model:value="formData.workMinutes"
                    placeholder="0"
                    :min="0"
                    :max="59"
                    style="width: 80px"
                  />
                  <span>分钟</span>
                </div>
              </a-form-item>
            </div>

            <a-form-item label="标准效率" name="standardEfficiency">
              <a-input v-model:value="formData.standardEfficiency" placeholder="未配置" />
            </a-form-item>

            <a-form-item label="实际效率" name="actualEfficiency">
              <a-input
                v-model:value="formData.actualEfficiency"
                placeholder="暂无计算数，无法计算效率"
              />
            </a-form-item>

            <a-form-item label="达标率" name="achievementRate">
              <a-input v-model:value="formData.achievementRate" placeholder="" />
            </a-form-item>
          </div>
        </div>

        <div class="row-item">
          <!-- 绩效信息 -->
          <div class="section">
            <h3 class="section-title">绩效信息</h3>
            <a-form-item label="计价方式" name="calculationMethod">
              <a-radio-group v-model:value="formData.calculationMethod">
                <a-radio value="计件">计件</a-radio>
                <a-radio value="计时">计时</a-radio>
              </a-radio-group>
            </a-form-item>

            <a-form-item label="工资单价" name="unitPrice">
              <div style="display: flex;">
                <a-input-number
                  v-model:value="formData.unitPrice"
                  placeholder="请选择"
                  style="width: 100%"
                  :precision="2"
                />
                <a-button style="margin-left: 4px;" :icon="h(RedoOutlined)">刷新</a-button>
              </div>
            </a-form-item>

            <a-form-item label="预计工资" name="estimatedSalary">
              <a-input v-model:value="formData.estimatedSalary" placeholder="" />
            </a-form-item>

            <a-form-item label="审批状态" name="approvalStatus">
              <a-radio-group v-model:value="formData.approvalStatus">
                <a-radio value="未提报">未提报</a-radio>
                <a-radio value="已提报">已提报</a-radio>
              </a-radio-group>
            </a-form-item>

            <a-form-item label="审批人" name="reviewer">
              <a-input v-model:value="formData.reviewer" placeholder="吴敏实际" />
            </a-form-item>

            <a-form-item label="报工创建时间" name="createTime">
              <a-date-picker
                v-model:value="formData.createTime"
                show-time
                format="YYYY-MM-DD HH:mm"
                placeholder="2025-07-28 14:05"
                style="width: 100%"
              />
            </a-form-item>

            <a-form-item label="辅助参数01" name="auxiliaryField01">
              <a-input v-model:value="formData.auxiliaryField01" placeholder="" />
            </a-form-item>

            <a-form-item label="辅助参数02" name="auxiliaryField02">
              <a-input v-model:value="formData.auxiliaryField02" placeholder="" />
            </a-form-item>

            <a-form-item label="辅助参数03" name="auxiliaryField03">
              <a-input v-model:value="formData.auxiliaryField03" placeholder="" />
            </a-form-item>

            <a-form-item label="辅助参数04" name="auxiliaryField04">
              <a-input v-model:value="formData.auxiliaryField04" placeholder="" />
            </a-form-item>

            <a-form-item label="辅助参数05" name="auxiliaryField05">
              <a-input v-model:value="formData.auxiliaryField05" placeholder="" />
            </a-form-item>
          </div>

          <!-- 自定义字段信息 -->
          <div class="section">
            <h3 class="section-title">自定义字段信息</h3>
            <a-form-item label="生产机台" name="productionStation">
              <a-select v-model:value="formData.productionStation" placeholder="请选择">
                <a-select-option value="机台1">机台1</a-select-option>
                <a-select-option value="机台2">机台2</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="版次" name="version">
              <a-select v-model:value="formData.version" placeholder="请选择">
                <a-select-option value="V1.0">V1.0</a-select-option>
                <a-select-option value="V2.0">V2.0</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="购料工费" name="materialCost">
              <a-input v-model:value="formData.materialCost" placeholder="" />
            </a-form-item>

            <a-form-item label="备注" name="remarks">
              <a-select v-model:value="formData.remarks" placeholder="请选择">
                <a-select-option value="备注1">备注1</a-select-option>
                <a-select-option value="备注2">备注2</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="短期方案" name="shortTermPlan">
              <a-input v-model:value="formData.shortTermPlan" placeholder="请选择" />
            </a-form-item>

            <a-form-item label="长期方案" name="longTermPlan">
              <a-textarea
                v-model:value="formData.longTermPlan"
                placeholder="该选项需要填写具体方法"
                :rows="3"
              />
            </a-form-item>

            <a-form-item label="常见问题" name="commonIssues">
              <div class="file-upload">
                <a-upload
                  v-model:file-list="fileList"
                  :before-upload="beforeUpload"
                  :multiple="true"
                >
                  <a-button>
                    <upload-outlined />
                    选择文件
                  </a-button>
                </a-upload>
                <div class="upload-tip">Ctrl+V 粘贴或拖拽到此处</div>
              </div>
            </a-form-item>
          </div>
        </div>
      </a-form>
    </div>
    <!-- 表单操作按钮 -->
    <div class="form-actions">
      <a-button @click="onCancel">取消</a-button>
      <a-button v-if="type === 'create'" type="primary" @click="onContinue">继续报工</a-button>
      <a-button type="primary" html-type="submit">保存</a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { h, ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'

import { message } from 'ant-design-vue'
import { UploadOutlined, RedoOutlined } from '@ant-design/icons-vue'
import type { FormInstance, UploadProps } from 'ant-design-vue'

import dayjs, { type Dayjs } from 'dayjs'

const type = ref<'create' | 'edit'>('create')
const router = useRouter()
const formRef = ref<FormInstance>()
const fileList = ref([])

onMounted(() => {
  // 有id说明是编辑模式
  const id = router.currentRoute.value.params.id
  if (id) {
    console.log(id)
    type.value = 'edit'
  }
})

// 表单数据
const formData = reactive({
  workOrderNumber: '',
  productNumber: '',
  processName: '',
  productName: '',
  processStatus: '未开始',
  productSpec: '',
  worker: '',
  startTime: null as Dayjs | null,
  workStation: '',
  endTime: null as Dayjs | null,
  workCount: '', // 添加缺失的字段
  unit: '',
  workHours: 0,
  workMinutes: 0,
  goodCount: '',
  standardEfficiency: '',
  defectCount: '',
  actualEfficiency: '',
  defectItems: '',
  achievementRate: '',
  processProgress: '',
  calculationMethod: '计件',
  productionStation: '',
  unitPrice: null,
  estimatedSalary: '', // 添加缺失的字段
  version: '',
  materialCost: '',
  remarks: '',
  approvalStatus: '未提报',
  shortTermPlan: '',
  reviewer: '',
  longTermPlan: '',
  createTime: dayjs(),
  commonIssues: '',
  auxiliaryField01: '',
  auxiliaryField02: '',
  auxiliaryField03: '',
  auxiliaryField04: '',
  auxiliaryField05: '',
})

// 表单验证规则
const rules = {
  workOrderNumber: [{ required: true, message: '请选择工单编号', trigger: 'change' }],
  processName: [{ required: true, message: '请选择工序名称', trigger: 'change' }],
  worker: [{ required: true, message: '请选择生产人员', trigger: 'change' }],
}

const goBack = () => {
  router.push('/produce/report')
}

const onFinish = (values: any) => {
  console.log('表单提交:', values)
  message.success('报工保存成功！')
  router.push('/produce/report')
}

const onFinishFailed = (errorInfo: any) => {
  console.log('表单验证失败:', errorInfo)
  message.error('请填写必填字段')
}

const onCancel = () => {
  router.push('/produce/report')
}

const onContinue = () => {
  formRef.value
    ?.validateFields()
    .then((values) => {
      console.log('继续报工:', values)
      message.success('当前报工已保存，可以继续下一个报工')
      // 重置表单但保留一些基础信息
      const preservedFields = {
        worker: formData.worker,
        workStation: formData.workStation,
        unit: formData.unit,
      }
      formRef.value?.resetFields()
      Object.assign(formData, preservedFields)
    })
    .catch((errorInfo) => {
      console.log('表单验证失败:', errorInfo)
      message.error('请填写必填字段')
    })
}

const beforeUpload: UploadProps['beforeUpload'] = (file) => {
  const isValidType =
    file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'application/pdf'
  if (!isValidType) {
    message.error('只能上传 JPG/PNG/PDF 格式的文件!')
  }
  const isLt2M = file.size / 1024 / 1024 < 2
  if (!isLt2M) {
    message.error('文件大小不能超过 2MB!')
  }
  return isValidType && isLt2M
}
</script>

<style scoped lang="scss">
.add-report-container {
  min-height: 100vh;
}

.row-item {
  display: flex;
  justify-content: flex-start;
  gap: 100px; // 使用gap替代margin-right
}

.form-container {
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section {
  flex: 0 0 400px; // 固定宽度400px，不伸缩
  margin-bottom: 32px;

  &:last-child {
    margin-bottom: 0;
  }

  // 为表单项设置合理的控件宽度
  :deep(.ant-form-item-control-input) {
    max-width: 280px; // 限制输入框最大宽度
  }

  // 时间选择器和数字输入框保持合适宽度
  :deep(.ant-picker) {
    max-width: 280px;
  }

  // 数字输入框在时间输入中保持较小宽度
  .time-inputs {
    :deep(.ant-input-number) {
      width: 80px !important;
    }
  }
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px 24px;

  .ant-form-item {
    margin-bottom: 0;
  }
}

.custom-fields {
  grid-template-columns: repeat(2, 1fr);
}

.required {
  :deep(.ant-form-item-label > label::before) {
    content: '*';
    color: #ff4d4f;
    margin-right: 4px;
  }
}

.process-status,
.calculation-method,
.approval-status {
  display: flex;
  flex-direction: column;
  gap: 8px;

  label {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
  }

  .status-options,
  .method-options {
    .ant-radio-group {
      display: flex;
      gap: 16px;
    }
  }
}

.work-time-input {
  display: flex;
  flex-direction: column;
  gap: 8px;

  label {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
  }

  .time-inputs {
    display: flex;
    align-items: center;
    gap: 8px;

    span {
      font-size: 14px;
      color: rgba(0, 0, 0, 0.65);
    }
  }
}

.file-upload {
  .upload-tip {
    margin-top: 8px;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.45);
  }
}

.form-actions {
  position: sticky;
  bottom: 0;
  right: 0;
  display: flex;
  justify-content: center;
  gap: 24px;
  padding: 16px 24px;
  background: white;
  box-shadow: 0 -1px 6px rgba(0, 0, 0, .08);

  .ant-btn {
    min-width: 100px;
  }
}

// 处理侧边栏折叠状态（ant-design默认折叠宽度为80px）
@media (max-width: 1200px) {
  .form-actions {
    left: 80px; // 折叠状态下的侧边栏宽度
  }
}

// 处理更小屏幕，可能需要调整为64px
@media (max-width: 992px) {
  .form-actions {
    left: 64px; // 更紧凑的折叠状态
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .row-item {
    gap: 60px; // 减少间距
  }

  .section {
    flex: 0 0 350px; // 稍微减少宽度
  }

  .form-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 992px) {
  .row-item {
    flex-direction: column; // 垂直排列
    gap: 32px;
  }

  .section {
    flex: 1 1 auto; // 在较小屏幕上允许伸缩
    max-width: 100%;

    :deep(.ant-form-item-control-input) {
      max-width: 100%; // 小屏幕上允许全宽
    }
  }
}

@media (max-width: 768px) {
  .add-report-container {
    padding-bottom: 120px; // 在小屏幕上增加底部空间，因为按钮会堆叠
  }

  .form-actions {
    left: 0; // 移动端侧边栏可能完全隐藏
    flex-direction: column;
    align-items: center;
    padding: 12px 16px; // 减少padding

    .ant-btn {
      width: 200px;
      margin-bottom: 8px; // 按钮间距

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .form-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px 16px;
  }

  .custom-fields {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .form-grid {
    grid-template-columns: 1fr;
  }

  .add-report-container {
    padding: 12px;
  }

  .form-container {
    padding: 16px;
  }
}
</style>
