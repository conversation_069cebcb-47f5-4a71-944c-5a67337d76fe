<template>
  <div class="view-report-container">
    <div class="content-container">
      <!-- 基础信息 -->
      <h3 class="section-title">基础信息</h3>
      <div class="row-item">
        <div class="section">
          <div class="info-item">
            <label>工单编号</label>
            <span class="info-value">{{ reportData.workOrderNumber }}</span>
          </div>

          <div class="info-item">
            <label>工序名称</label>
            <span class="info-value">{{ reportData.processName }}</span>
          </div>

          <div class="info-item">
            <label>工序状态</label>
            <span class="info-value">
              {{ reportData.processStatus }}
            </span>
          </div>

          <div class="info-item">
            <label>生产人员</label>
            <span class="info-value">{{ reportData.worker }}</span>
          </div>

          <div class="info-item">
            <label>报工数</label>
            <span class="info-value">{{ reportData.workCount }}</span>
          </div>

          <div class="info-item">
            <label>单位</label>
            <span class="info-value">{{ reportData.unit }}</span>
          </div>

          <div class="info-item">
            <label>良品数</label>
            <span class="info-value good-count">{{ reportData.goodCount }}</span>
          </div>

          <div class="info-item">
            <label>不良品数</label>
            <span class="info-value defect-count">{{ reportData.defectCount }}</span>
          </div>

          <div class="info-item">
            <label>不良品项</label>
            <span class="info-value">
              <a-tag>{{ reportData.defectItems }}</a-tag>
            </span>
          </div>

          <div class="info-item">
            <label>工序进度</label>
            <span class="info-value">{{ reportData.processProgress }}</span>
          </div>
        </div>

        <div class="section">
          <div class="info-item">
            <label>产品编号</label>
            <span class="info-value">{{ reportData.productNumber }}</span>
          </div>

          <div class="info-item">
            <label>产品名称</label>
            <span class="info-value">{{ reportData.productName }}</span>
          </div>

          <div class="info-item">
            <label>产品规格</label>
            <span class="info-value">{{ reportData.productSpec }}</span>
          </div>

          <div class="info-item">
            <label>开始时间</label>
            <span class="info-value">{{ formatDateTime(reportData.startTime) }}</span>
          </div>

          <div class="info-item">
            <label>结束时间</label>
            <span class="info-value">{{ formatDateTime(reportData.endTime) }}</span>
          </div>

          <div class="info-item">
            <label>报工时长</label>
            <span class="info-value">{{
              formatWorkDuration(reportData.workHours, reportData.workMinutes)
            }}</span>
          </div>

          <div class="info-item">
            <label>标准效率</label>
            <span class="info-value">{{ reportData.standardEfficiency }}</span>
          </div>

          <div class="info-item">
            <label>实际效率</label>
            <span class="info-value">{{ reportData.actualEfficiency }}</span>
          </div>

          <div class="info-item">
            <label>达标率</label>
            <span class="info-value">{{ reportData.achievementRate }}</span>
          </div>

          <div class="info-item">
            <label>创建人</label>
            <span class="info-value">{{ reportData.achievementRate }}</span>
          </div>

          <div class="info-item">
            <label>创建时间</label>
            <span class="info-value">{{ reportData.achievementRate }}</span>
          </div>

          <div class="info-item">
            <label>更新人</label>
            <span class="info-value">{{ reportData.achievementRate }}</span>
          </div>

          <div class="info-item">
            <label>更新时间</label>
            <span class="info-value">{{ reportData.achievementRate }}</span>
          </div>
        </div>
      </div>

      <div class="row-item">
        <!-- 绩效信息 -->
        <div class="section">
          <h3 class="section-title">绩效信息</h3>
          <div class="info-item">
            <label>计价方式</label>
            <span class="info-value">{{ reportData.calculationMethod }}</span>
          </div>

          <div class="info-item">
            <label>工资单价</label>
            <span class="info-value">{{ reportData.unitPrice }}元</span>
          </div>

          <div class="info-item">
            <label>预计工资</label>
            <span class="info-value">{{ reportData.estimatedSalary }}元</span>
          </div>

          <div class="info-item">
            <label>审批状态</label>
            <span class="info-value">
              {{ reportData.approvalStatus }}
            </span>
          </div>

          <div class="info-item">
            <label>审批人</label>
            <span class="info-value">{{ reportData.reviewer }}</span>
          </div>

          <div class="info-item">
            <label>报工创建时间</label>
            <span class="info-value">{{ formatDateTime(reportData.createTime) }}</span>
          </div>

          <div class="info-item">
            <label>辅助参数01</label>
            <span class="info-value">{{ reportData.auxiliaryField01 }}</span>
          </div>

          <div class="info-item">
            <label>辅助参数02</label>
            <span class="info-value">{{ reportData.auxiliaryField02 }}</span>
          </div>

          <div class="info-item">
            <label>辅助参数03</label>
            <span class="info-value">{{ reportData.auxiliaryField03 }}</span>
          </div>

          <div class="info-item">
            <label>辅助参数04</label>
            <span class="info-value">{{ reportData.auxiliaryField04 }}</span>
          </div>

          <div class="info-item">
            <label>辅助参数05</label>
            <span class="info-value">{{ reportData.auxiliaryField05 }}</span>
          </div>
        </div>

        <!-- 自定义字段信息 -->
        <div class="section">
          <h3 class="section-title">自定义字段信息</h3>
          <div class="info-item">
            <label>生产机台</label>
            <span class="info-value">{{ reportData.productionStation }}</span>
          </div>

          <div class="info-item">
            <label>版次</label>
            <span class="info-value">{{ reportData.version }}</span>
          </div>

          <div class="info-item">
            <label>购料工费</label>
            <span class="info-value">{{ reportData.materialCost }}</span>
          </div>

          <div class="info-item">
            <label>备注</label>
            <span class="info-value">{{ reportData.remarks }}</span>
          </div>

          <div class="info-item">
            <label>短期方案</label>
            <span class="info-value">{{ reportData.shortTermPlan }}</span>
          </div>

          <div class="info-item">
            <label>长期方案</label>
            <div class="info-value textarea-value">{{ reportData.longTermPlan }}</div>
          </div>

          <div class="info-item">
            <label>常见问题</label>
            <div class="info-value">
              <div v-if="reportData.fileList && reportData.fileList.length > 0" class="file-list">
                <div v-for="file in reportData.fileList" :key="file.uid" class="file-item">
                  <span class="file-name">{{ file.name }}</span>
                  <a-button type="link" size="small">下载</a-button>
                </div>
              </div>
              <span v-else class="empty-value">无附件</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部操作按钮 -->
    <div class="form-actions">
      <a-button @click="goBack">返回</a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import dayjs from 'dayjs'

const router = useRouter()

// 文件类型定义
interface FileItem {
  uid: string
  name: string
  url?: string
}

// 模拟的报工数据
const reportData = ref({
  workOrderNumber: 'GD20230007',
  productNumber: 'CP20230004',
  processName: '氧化',
  productName: 'G031',
  processStatus: '已结束',
  productSpec: '1250T',
  worker: '测试',
  startTime: '2025-07-22 17:49',
  workCount: '1604',
  endTime: '2025-07-22 17:49',
  unit: '只',
  workHours: 0,
  workMinutes: 0,
  goodCount: '1600',
  standardEfficiency: '未配置',
  defectCount: '4',
  actualEfficiency: '报工时长为0，无法计算效率',
  defectItems: '其他 4',
  achievementRate: '-',
  processProgress: '1600 / 1600',
  calculationMethod: '计件',
  unitPrice: 0.25,
  estimatedSalary: '',
  approvalStatus: '未提报',
  reviewer: '测试',
  createTime: '2025-07-22 17:50:06',
  auxiliaryField01: '',
  auxiliaryField02: '',
  auxiliaryField03: '',
  auxiliaryField04: '',
  auxiliaryField05: '',
  productionStation: '-',
  version: '-',
  materialCost: '',
  remarks: '',
  shortTermPlan: '',
  longTermPlan: '',
  fileList: [] as FileItem[],
})

onMounted(() => {
  // 获取报工详情数据
  const id = router.currentRoute.value.params.id
  if (id) {
    console.log('获取报工详情:', id)
    // 这里应该调用API获取数据
    // loadReportDetail(id)
  }
})

const formatDateTime = (dateStr: string) => {
  if (!dateStr) return '-'
  return dayjs(dateStr).format('YYYY-MM-DD HH:mm')
}

const formatWorkDuration = (hours: number, minutes: number) => {
  if (!hours && !minutes) return '0小时0分钟'
  return `${hours}小时${minutes}分钟`
}

const getApprovalStatusClass = (status: string) => {
  switch (status) {
    case '未提报':
      return 'approval-pending'
    case '已提报':
      return 'approval-submitted'
    case '已审批':
      return 'approval-approved'
    default:
      return ''
  }
}

const goBack = () => {
  router.push('/produce/report')
}

const goEdit = () => {
  const id = router.currentRoute.value.params.id
  router.push(`/produce/report/edit/${id}`)
}
</script>

<style scoped lang="scss">
.view-report-container {
  min-height: 100vh;
  padding-bottom: 80px; // 为底部固定按钮留出空间
}

.row-item {
  display: flex;
  justify-content: flex-start;
  gap: 100px; // 使用gap替代margin-right
  margin-bottom: 30px;
}

.content-container {
  background: white;
  padding: 24px 24px 0;
  border-radius: 8px;
}

.section {
  flex: 0 0 400px; // 固定宽度400px，不伸缩
  margin-bottom: 32px;

  &:last-child {
    margin-bottom: 0;
  }
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
  color: rgba(0, 0, 0, 0.85);
  position: relative;
}

.info-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
  min-height: 32px;

  &:last-child {
    margin-bottom: 0;
  }

  label {
    flex: 0 0 100px;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);
    line-height: 32px;
    margin-right: 12px;
  }

  .info-value {
    flex: 1;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
    line-height: 32px;
    word-break: break-word;

    &.empty-value {
      color: rgba(0, 0, 0, 0.45);
    }

    &.status {
      padding: 2px 8px;
      border-radius: 4px;
      font-size: 12px;
      line-height: 20px;
      display: inline-block;

      &.status-pending {
        background: #fff7e6;
        color: #fa8c16;
        border: 1px solid #ffd591;
      }

      &.status-progress {
        background: #e6f7ff;
        color: #1890ff;
        border: 1px solid #91d5ff;
      }

      &.status-completed {
        background: #f6ffed;
        color: #52c41a;
        border: 1px solid #b7eb8f;
      }

      &.approval-pending {
        background: #fff1f0;
        color: #ff4d4f;
        border: 1px solid #ffccc7;
      }

      &.approval-submitted {
        background: #e6f7ff;
        color: #1890ff;
        border: 1px solid #91d5ff;
      }

      &.approval-approved {
        background: #f6ffed;
        color: #52c41a;
        border: 1px solid #b7eb8f;
      }
    }

    &.good-count {
      color: #52c41a;
      font-weight: 500;
    }

    &.defect-count {
      color: #ff4d4f;
      font-weight: 500;
    }

    &.price {
      color: #fa8c16;
      font-weight: 500;
    }

    &.textarea-value {
      line-height: 1.6;
      white-space: pre-wrap;
      max-width: 280px;
    }
  }
}

.file-list {
  .file-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    background: #fafafa;
    border: 1px solid #f0f0f0;
    border-radius: 4px;
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }

    .file-name {
      flex: 1;
      font-size: 13px;
      color: rgba(0, 0, 0, 0.85);
    }
  }
}

.form-actions {
  display: flex;
  justify-content: center;
  background: white;

  .ant-btn {
    min-width: 100px;
  }
}

// 处理侧边栏折叠状态（ant-design默认折叠宽度为80px）
@media (max-width: 1200px) {
  .form-actions {
    left: 80px; // 折叠状态下的侧边栏宽度
  }
}

// 处理更小屏幕，可能需要调整为64px
@media (max-width: 992px) {
  .form-actions {
    left: 64px; // 更紧凑的折叠状态
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .row-item {
    gap: 60px; // 减少间距
  }

  .section {
    flex: 0 0 350px; // 稍微减少宽度
  }
}

@media (max-width: 992px) {
  .row-item {
    flex-direction: column; // 垂直排列
    gap: 32px;
  }

  .section {
    flex: 1 1 auto; // 在较小屏幕上允许伸缩
    max-width: 100%;
  }
}

@media (max-width: 768px) {
  .view-report-container {
    padding-bottom: 120px; // 在小屏幕上增加底部空间，因为按钮会堆叠
  }

  .form-actions {
    left: 0; // 移动端侧边栏可能完全隐藏
    flex-direction: column;
    align-items: center;
    padding: 12px 16px; // 减少padding

    .ant-btn {
      width: 200px;
      margin-bottom: 8px; // 按钮间距

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

@media (max-width: 480px) {
  .view-report-container {
    padding: 12px;
  }

  .content-container {
    padding: 16px;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;

    label {
      margin-bottom: 4px;
      line-height: 1.4;
    }

    .info-value {
      line-height: 1.4;
    }
  }
}
</style>
