import type { TableColumn } from '@/common/types'

export const tableColumns: TableColumn[] = [
  {
    title: '任务计划数',
    dataIndex: 'plannedTaskQuantity',
    key: 'plannedTaskQuantity',
    visible: false,
    width: 120,
  },
  {
    title: '报工数量',
    dataIndex: 'reportedQuantity',
    key: 'reportedQuantity',
    visible: true,
    width: 120,
  },
  {
    title: '计价方式',
    dataIndex: 'pricingMethod',
    key: 'pricingMethod',
    visible: true,
    width: 120,
  },
  {
    title: '工资单价',
    dataIndex: 'wageUnitPrice',
    key: 'wageUnitPrice',
    visible: true,
    width: 120,
  },
  {
    title: '预计工资',
    dataIndex: 'estimatedWage',
    key: 'estimatedWage',
    visible: true,
    width: 120,
  },
  {
    title: '良品数量',
    dataIndex: 'qualifiedQuantity',
    key: 'qualifiedQuantity',
    visible: true,
    width: 120,
  },
  {
    title: '不良品数量',
    dataIndex: 'defectiveQuantity',
    key: 'defectiveQuantity',
    visible: true,
    width: 120,
  },
  {
    title: '不良品项',
    dataIndex: 'defectiveItems',
    key: 'defectiveItems',
    visible: false,
    width: 120,
  },
  {
    title: '报工单位',
    dataIndex: 'reportUnit',
    key: 'reportUnit',
    visible: true,
    width: 120,
  },
  {
    title: '生产人员',
    dataIndex: 'productionStaff',
    key: 'productionStaff',
    visible: true,
    width: 120,
  },
  {
    title: '部门',
    dataIndex: 'department',
    key: 'department',
    visible: false,
    width: 120,
  },
  {
    title: '报工开始时间',
    dataIndex: 'reportStartTime',
    key: 'reportStartTime',
    visible: true,
    width: 120,
  },
  {
    title: '报工结束时间',
    dataIndex: 'reportEndTime',
    key: 'reportEndTime',
    visible: true,
    width: 120,
  },
  {
    title: '报工时长',
    dataIndex: 'reportDuration',
    key: 'reportDuration',
    visible: true,
    width: 120,
  },
  {
    title: '标准效率',
    dataIndex: 'standardEfficiency',
    key: 'standardEfficiency',
    visible: true,
    width: 120,
  },
  {
    title: '实际效率',
    dataIndex: 'actualEfficiency',
    key: 'actualEfficiency',
    visible: true,
    width: 120,
  },
  {
    title: '达标率',
    dataIndex: 'achievementRate',
    key: 'achievementRate',
    visible: true,
    width: 120,
  },
  {
    title: '审批状态',
    dataIndex: 'approvalStatus',
    key: 'approvalStatus',
    visible: true,
    width: 120,
  },
  {
    title: '审批时间',
    dataIndex: 'approvalTime',
    key: 'approvalTime',
    visible: true,
    width: 120,
  },
  {
    title: '审批人',
    dataIndex: 'approver',
    key: 'approver',
    visible: true,
    width: 120,
  },
  {
    title: '创建人',
    dataIndex: 'creator',
    key: 'creator',
    visible: true,
    width: 120,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    visible: true,
    width: 120,
  },
  {
    title: '任务计划数',
    dataIndex: 'taskPlanQuantity',
    key: 'taskPlanQuantity',
    visible: true,
    width: 120,
  },
  {
    title: '更新人',
    dataIndex: 'updater',
    key: 'updater',
    visible: true,
    width: 120,
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    key: 'updateTime',
    visible: true,
    width: 120,
  },
  {
    title: '备注',
    dataIndex: 'remark',
    key: 'remark',
    visible: true,
    width: 120,
  },
  {
    title: '工序名称',
    dataIndex: 'processName',
    key: 'processName',
    visible: true,
    width: 120,
  },
  {
    title: '工序状态',
    dataIndex: 'processStatus',
    key: 'processStatus',
    visible: true,
    width: 120,
  },
  {
    title: '工单编号',
    dataIndex: 'workOrderNo',
    key: 'workOrderNo',
    visible: true,
    width: 120,
  },
  {
    title: '产品编号',
    dataIndex: 'productNo',
    key: 'productNo',
    visible: true,
    width: 120,
  },
  {
    title: '产品名称',
    dataIndex: 'productName',
    key: 'productName',
    visible: true,
    width: 120,
  },
  {
    title: '产品规格',
    dataIndex: 'productSpecification',
    key: 'productSpecification',
    visible: true,
    width: 120,
  },
  {
    title: '操作',
    key: 'action',
    visible: true,
    width: 120,
    fixed: 'right',
  },
]