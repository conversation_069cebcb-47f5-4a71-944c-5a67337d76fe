.report-page {
  height: 100%;
  overflow: auto;
}

.search-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 24px;
  margin-top: $spacing-base;
  padding: 0 $spacing-lg;
}

.filter-row {
  margin-top: $spacing-base;
  padding: 0 $spacing-lg;
}

.list-action-wrapper {
  flex-shrink: 0; // 防止搜索区域被压缩
}

.list-table-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  margin-top: $spacing-md;
  padding: 0 $spacing-lg $spacing-md;

  .custom-btn {
    display: flex;
    align-items: center;
    gap: 4px;
  }
}

:deep(.list-table-header__left .ant-btn-text) {
  padding: 4px 8px;
}

:deep(.list-table-body .ant-btn-link) {
  padding: 4px 8px;
}

// 字段配置下拉面板样式
.column-config-dropdown {
  padding: 0;

  :deep(.ant-dropdown-menu) {
    padding: 0;
    box-shadow: none;
  }
}

// 确保下拉菜单不会因为内部点击而关闭
:deep(.ant-dropdown) {
  .ant-dropdown-menu {
    .ant-dropdown-menu-item {
      padding: 0;
    }
  }
}
