<template>
  <div class="data-filter-demo">
    <div class="demo-header">
      <h2>数据过滤组件演示</h2>
      <p>这是一个数据过滤组件的演示页面，展示了不同字段类型的过滤功能。</p>
    </div>

    <div class="demo-content">
      <!-- 单个过滤条件演示 -->
      <div class="demo-section">
        <h3>单个过滤条件</h3>
        <div class="demo-description">
          <p>演示单个数据过滤条件的使用，支持input、number、select三种字段类型。</p>
        </div>
        <div class="demo-example">
          <DataFilter @remove="handleSingleRemove" @change="handleSingleChange" />
        </div>
        <div class="demo-result">
          <h4>过滤结果：</h4>
          <pre>{{ JSON.stringify(singleFilterData, null, 2) }}</pre>
        </div>
      </div>

      <!-- 数据过滤面板演示 -->
      <div class="demo-section">
        <h3>数据过滤面板</h3>
        <div class="demo-description">
          <p>演示完整的数据过滤面板，支持多个过滤条件的组合使用，包含逻辑连接符（且/或）。</p>
        </div>
        <div class="demo-example">
          <DataFilterPanel
            v-model="panelFilters"
            @apply="handlePanelApply"
            @clear="handlePanelClear"
          />
        </div>
        <div class="demo-result">
          <h4>面板过滤结果：</h4>
          <pre>{{ JSON.stringify(panelFilters, null, 2) }}</pre>
        </div>
        <div class="demo-result" v-if="appliedFilters.length > 0">
          <h4>应用的过滤条件：</h4>
          <pre>{{ JSON.stringify(appliedFilters, null, 2) }}</pre>
        </div>
      </div>

      <!-- 多个过滤条件演示 -->
      <div class="demo-section">
        <h3>简单多条件过滤</h3>
        <div class="demo-description">
          <p>演示简单的多个数据过滤条件组合使用，可以动态添加和删除过滤条件。</p>
        </div>
        <div class="demo-example">
          <div v-for="(filter, index) in multipleFilters" :key="index" class="filter-item">
            <DataFilter
              @remove="() => removeMultipleFilter(index)"
              @change="(data) => updateMultipleFilter(index, data)"
            />
          </div>
          <a-button
            type="dashed"
            @click="addMultipleFilter"
            style="margin-top: 12px; width: 100%;"
          >
            + 添加筛选条件
          </a-button>
        </div>
        <div class="demo-result">
          <h4>过滤结果：</h4>
          <pre>{{ JSON.stringify(multipleFilters, null, 2) }}</pre>
        </div>
      </div>

      <!-- 功能说明 -->
      <div class="demo-section">
        <h3>功能说明</h3>
        <div class="feature-list">
          <div class="feature-item">
            <h4>支持的字段类型：</h4>
            <ul>
              <li><strong>input类型：</strong>支持等于、不等于、包含、不包含、为空、不为空条件</li>
              <li><strong>number类型：</strong>支持在范围内、不在范围内、为空、不为空条件</li>
              <li><strong>select类型：</strong>支持是其中一个、不是任何一个、为空、不为空条件</li>
            </ul>
          </div>
          <div class="feature-item">
            <h4>交互特性：</h4>
            <ul>
              <li>字段选择变化时，条件选项会根据字段类型动态更新</li>
              <li>条件选择变化时，输入控件会根据条件类型动态显示/隐藏</li>
              <li>选择"为空"或"不为空"时，不显示输入控件</li>
              <li>number类型的范围条件会显示两个数字输入框</li>
              <li>select类型支持多选</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import DataFilter from '@/components/QuickSearchTabbar/DataFilter.vue'
import DataFilterPanel from '@/components/QuickSearchTabbar/DataFilterPanel.vue'

// 单个过滤条件数据
const singleFilterData = ref<any>({})

// 数据过滤面板数据
const panelFilters = ref<any[]>([])
const appliedFilters = ref<any[]>([])

// 多个过滤条件数据
const multipleFilters = ref<any[]>([{}])

// 单个过滤条件事件处理
const handleSingleRemove = () => {
  console.log('移除单个过滤条件')
  singleFilterData.value = {}
}

const handleSingleChange = (data: any) => {
  console.log('单个过滤条件变化:', data)
  singleFilterData.value = data
}

// 数据过滤面板事件处理
const handlePanelApply = (filters: any[]) => {
  console.log('应用过滤条件:', filters)
  appliedFilters.value = filters
}

const handlePanelClear = () => {
  console.log('清空过滤条件')
  appliedFilters.value = []
}

// 多个过滤条件事件处理
const addMultipleFilter = () => {
  multipleFilters.value.push({})
}

const removeMultipleFilter = (index: number) => {
  multipleFilters.value.splice(index, 1)
  if (multipleFilters.value.length === 0) {
    multipleFilters.value.push({})
  }
}

const updateMultipleFilter = (index: number, data: any) => {
  multipleFilters.value[index] = data
}
</script>

<style lang="scss" scoped>
.data-filter-demo {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;

  .demo-header {
    margin-bottom: 32px;
    text-align: center;

    h2 {
      color: #1890ff;
      margin-bottom: 8px;
    }

    p {
      color: #666;
      font-size: 16px;
    }
  }

  .demo-content {
    .demo-section {
      margin-bottom: 48px;
      padding: 24px;
      border: 1px solid #e8e8e8;
      border-radius: 8px;
      background-color: #fafafa;

      h3 {
        margin-bottom: 16px;
        color: #333;
        border-bottom: 2px solid #1890ff;
        padding-bottom: 8px;
      }

      .demo-description {
        margin-bottom: 20px;
        
        p {
          color: #666;
          line-height: 1.6;
        }
      }

      .demo-example {
        margin-bottom: 20px;
        padding: 16px;
        background-color: white;
        border-radius: 6px;
        border: 1px solid #e8e8e8;

        .filter-item {
          margin-bottom: 8px;
          
          &:last-child {
            margin-bottom: 0;
          }
        }
      }

      .demo-result {
        h4 {
          margin-bottom: 12px;
          color: #333;
        }

        pre {
          background-color: #f5f5f5;
          padding: 16px;
          border-radius: 6px;
          font-size: 12px;
          overflow-x: auto;
          border: 1px solid #e8e8e8;
        }
      }

      .feature-list {
        .feature-item {
          margin-bottom: 24px;

          h4 {
            margin-bottom: 12px;
            color: #333;
          }

          ul {
            padding-left: 20px;

            li {
              margin-bottom: 8px;
              line-height: 1.6;
              color: #666;

              strong {
                color: #333;
              }
            }
          }
        }
      }
    }
  }
}
</style>
