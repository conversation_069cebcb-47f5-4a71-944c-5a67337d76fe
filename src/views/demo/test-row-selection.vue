<template>
  <div class="test-row-selection">
    <h2>ConfigurableTable 行选择功能测试</h2>
    
    <div class="test-section">
      <h3>1. 多选模式 (checkbox)</h3>
      <ConfigurableTable
        :columns="testColumns"
        :data-source="testData"
        :row-selection="{ type: 'checkbox' }"
        @selection-change="onCheckboxSelectionChange"
      >
        <template #selection-toolbar>
          <a-button type="primary" size="small">批量操作</a-button>
          <a-button size="small">导出选中</a-button>
        </template>
      </ConfigurableTable>
      
      <div class="selection-info">
        <p>选中的行: {{ JSON.stringify(checkboxSelectedKeys) }}</p>
      </div>
    </div>

    <div class="test-section">
      <h3>2. 单选模式 (radio)</h3>
      <ConfigurableTable
        :columns="testColumns"
        :data-source="testData"
        :row-selection="{ type: 'radio' }"
        @selection-change="onRadioSelectionChange"
      />
      
      <div class="selection-info">
        <p>选中的行: {{ JSON.stringify(radioSelectedKeys) }}</p>
      </div>
    </div>

    <div class="test-section">
      <h3>3. 禁用行选择</h3>
      <ConfigurableTable
        :columns="testColumns"
        :data-source="testData"
        :row-selection="false"
      />
    </div>

    <div class="test-section">
      <h3>4. 无效配置 (应该禁用行选择)</h3>
      <ConfigurableTable
        :columns="testColumns"
        :data-source="testData"
        :row-selection="{ type: 'invalid' }"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ConfigurableTable from '@/components/ConfigurableTable/index.vue'
import type { TableColumn } from '@/common/types'

// 测试数据
const testColumns: TableColumn[] = [
  {
    title: 'ID',
    dataIndex: 'id',
    key: 'id',
    visible: true,
    width: 80,
  },
  {
    title: '姓名',
    dataIndex: 'name',
    key: 'name',
    visible: true,
    width: 120,
  },
  {
    title: '年龄',
    dataIndex: 'age',
    key: 'age',
    visible: true,
    width: 80,
  },
  {
    title: '职位',
    dataIndex: 'position',
    key: 'position',
    visible: true,
    width: 150,
  },
  {
    title: '部门',
    dataIndex: 'department',
    key: 'department',
    visible: true,
    width: 120,
  },
]

const testData = [
  { id: 1, name: '张三', age: 28, position: '前端工程师', department: '技术部' },
  { id: 2, name: '李四', age: 32, position: '后端工程师', department: '技术部' },
  { id: 3, name: '王五', age: 25, position: '产品经理', department: '产品部' },
  { id: 4, name: '赵六', age: 30, position: '设计师', department: '设计部' },
  { id: 5, name: '钱七', age: 27, position: '测试工程师', department: '技术部' },
]

// 选择状态
const checkboxSelectedKeys = ref<(string | number)[]>([])
const radioSelectedKeys = ref<(string | number)[]>([])

// 事件处理
const onCheckboxSelectionChange = (keys: (string | number)[], selectedRows: any[]) => {
  checkboxSelectedKeys.value = keys
  console.log('Checkbox selection changed:', keys, selectedRows)
}

const onRadioSelectionChange = (keys: (string | number)[], selectedRows: any[]) => {
  radioSelectedKeys.value = keys
  console.log('Radio selection changed:', keys, selectedRows)
}
</script>

<style scoped>
.test-row-selection {
  padding: 20px;
}

.test-section {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
}

.test-section h3 {
  margin-top: 0;
  color: #1890ff;
}

.selection-info {
  margin-top: 16px;
  padding: 12px;
  background-color: #f6f6f6;
  border-radius: 4px;
}

.selection-info p {
  margin: 0;
  font-family: monospace;
  font-size: 12px;
}
</style>
