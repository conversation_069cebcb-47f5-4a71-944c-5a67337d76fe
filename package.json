{"name": "xiaogongdan-web", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite --open", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "format": "prettier --write src/"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "ant-design-vue": "~4.2.6", "dayjs": "^1.11.13", "lodash": "^4.17.21", "pinia": "^3.0.3", "vue": "^3.5.17", "vue-router": "^4.5.1", "vuedraggable": "^4.1.0"}, "devDependencies": {"@tsconfig/node22": "^22.0.2", "@types/lodash": "^4.17.20", "@types/node": "^22.15.32", "@vitejs/plugin-vue": "^6.0.0", "@vue/tsconfig": "^0.7.0", "less": "^4.4.0", "npm-run-all2": "^8.0.4", "prettier": "3.5.3", "sass-embedded": "^1.89.2", "typescript": "~5.8.0", "vite": "^7.0.0", "vite-plugin-vue-devtools": "^7.7.7", "vue-tsc": "^2.2.10"}}